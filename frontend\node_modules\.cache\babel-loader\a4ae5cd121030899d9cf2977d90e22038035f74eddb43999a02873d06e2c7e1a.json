{"ast": null, "code": "'use client';\n\nimport PropTypes from 'prop-types';\nimport ClassNameGenerator from '@mui/utils/ClassNameGenerator';\nimport createBox from '../createBox';\nimport boxClasses from './boxClasses';\nconst Box = createBox({\n  defaultClassName: boxClasses.root,\n  generateClassName: ClassNameGenerator.generate\n});\nprocess.env.NODE_ENV !== \"production\" ? Box.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Box;", "map": {"version": 3, "names": ["PropTypes", "ClassNameGenerator", "createBox", "boxClasses", "Box", "defaultClassName", "root", "generateClassName", "generate", "process", "env", "NODE_ENV", "propTypes", "children", "node", "component", "elementType", "sx", "oneOfType", "arrayOf", "func", "object", "bool"], "sources": ["F:/my_work/game_logic_preview/frontend/node_modules/@mui/system/esm/Box/Box.js"], "sourcesContent": ["'use client';\n\nimport PropTypes from 'prop-types';\nimport ClassNameGenerator from '@mui/utils/ClassNameGenerator';\nimport createBox from '../createBox';\nimport boxClasses from './boxClasses';\nconst Box = createBox({\n  defaultClassName: boxClasses.root,\n  generateClassName: ClassNameGenerator.generate\n});\nprocess.env.NODE_ENV !== \"production\" ? Box.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Box;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,SAAS,MAAM,YAAY;AAClC,OAAOC,kBAAkB,MAAM,+BAA+B;AAC9D,OAAOC,SAAS,MAAM,cAAc;AACpC,OAAOC,UAAU,MAAM,cAAc;AACrC,MAAMC,GAAG,GAAGF,SAAS,CAAC;EACpBG,gBAAgB,EAAEF,UAAU,CAACG,IAAI;EACjCC,iBAAiB,EAAEN,kBAAkB,CAACO;AACxC,CAAC,CAAC;AACFC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGP,GAAG,CAACQ,SAAS,CAAC,yBAAyB;EAC7E;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAEb,SAAS,CAACc,IAAI;EACxB;AACF;AACA;AACA;EACEC,SAAS,EAAEf,SAAS,CAACgB,WAAW;EAChC;AACF;AACA;EACEC,EAAE,EAAEjB,SAAS,CAACkB,SAAS,CAAC,CAAClB,SAAS,CAACmB,OAAO,CAACnB,SAAS,CAACkB,SAAS,CAAC,CAAClB,SAAS,CAACoB,IAAI,EAAEpB,SAAS,CAACqB,MAAM,EAAErB,SAAS,CAACsB,IAAI,CAAC,CAAC,CAAC,EAAEtB,SAAS,CAACoB,IAAI,EAAEpB,SAAS,CAACqB,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAejB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}