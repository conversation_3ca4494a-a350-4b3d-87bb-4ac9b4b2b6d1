{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"BackdropProps\"],\n  _excluded2 = [\"anchor\", \"disableBackdropTransition\", \"disableDiscovery\", \"disableSwipeToOpen\", \"hideBackdrop\", \"hysteresis\", \"allowSwipeInChildren\", \"minFlingVelocity\", \"ModalProps\", \"onClose\", \"onOpen\", \"open\", \"PaperProps\", \"SwipeAreaProps\", \"swipeAreaWidth\", \"transitionDuration\", \"variant\"];\nimport * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport PropTypes from 'prop-types';\nimport elementTypeAcceptingRef from '@mui/utils/elementTypeAcceptingRef';\nimport NoSsr from '../NoSsr';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport Drawer, { getAnchor, isHorizontal } from '../Drawer/Drawer';\nimport useForkRef from '../utils/useForkRef';\nimport ownerDocument from '../utils/ownerDocument';\nimport ownerWindow from '../utils/ownerWindow';\nimport useEventCallback from '../utils/useEventCallback';\nimport useEnhancedEffect from '../utils/useEnhancedEffect';\nimport useTheme from '../styles/useTheme';\nimport { getTransitionProps } from '../transitions/utils';\nimport SwipeArea from './SwipeArea';\n\n// This value is closed to what browsers are using internally to\n// trigger a native scroll.\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst UNCERTAINTY_THRESHOLD = 3; // px\n\n// This is the part of the drawer displayed on touch start.\nconst DRAG_STARTED_SIGNAL = 20; // px\n\n// We can only have one instance at the time claiming ownership for handling the swipe.\n// Otherwise, the UX would be confusing.\n// That's why we use a singleton here.\nlet claimedSwipeInstance = null;\n\n// Exported for test purposes.\nexport function reset() {\n  claimedSwipeInstance = null;\n}\nfunction calculateCurrentX(anchor, touches, doc) {\n  return anchor === 'right' ? doc.body.offsetWidth - touches[0].pageX : touches[0].pageX;\n}\nfunction calculateCurrentY(anchor, touches, containerWindow) {\n  return anchor === 'bottom' ? containerWindow.innerHeight - touches[0].clientY : touches[0].clientY;\n}\nfunction getMaxTranslate(horizontalSwipe, paperInstance) {\n  return horizontalSwipe ? paperInstance.clientWidth : paperInstance.clientHeight;\n}\nfunction getTranslate(currentTranslate, startLocation, open, maxTranslate) {\n  return Math.min(Math.max(open ? startLocation - currentTranslate : maxTranslate + startLocation - currentTranslate, 0), maxTranslate);\n}\n\n/**\n * @param {Element | null} element\n * @param {Element} rootNode\n */\nfunction getDomTreeShapes(element, rootNode) {\n  // Adapted from https://github.com/oliviertassinari/react-swipeable-views/blob/7666de1dba253b896911adf2790ce51467670856/packages/react-swipeable-views/src/SwipeableViews.js#L129\n  const domTreeShapes = [];\n  while (element && element !== rootNode.parentElement) {\n    const style = ownerWindow(rootNode).getComputedStyle(element);\n    if (\n    // Ignore the scroll children if the element is absolute positioned.\n    style.getPropertyValue('position') === 'absolute' ||\n    // Ignore the scroll children if the element has an overflowX hidden\n    style.getPropertyValue('overflow-x') === 'hidden') {\n      // noop\n    } else if (element.clientWidth > 0 && element.scrollWidth > element.clientWidth || element.clientHeight > 0 && element.scrollHeight > element.clientHeight) {\n      // Ignore the nodes that have no width.\n      // Keep elements with a scroll\n      domTreeShapes.push(element);\n    }\n    element = element.parentElement;\n  }\n  return domTreeShapes;\n}\n\n/**\n * @param {object} param0\n * @param {ReturnType<getDomTreeShapes>} param0.domTreeShapes\n */\nfunction computeHasNativeHandler({\n  domTreeShapes,\n  start,\n  current,\n  anchor\n}) {\n  // Adapted from https://github.com/oliviertassinari/react-swipeable-views/blob/7666de1dba253b896911adf2790ce51467670856/packages/react-swipeable-views/src/SwipeableViews.js#L175\n  const axisProperties = {\n    scrollPosition: {\n      x: 'scrollLeft',\n      y: 'scrollTop'\n    },\n    scrollLength: {\n      x: 'scrollWidth',\n      y: 'scrollHeight'\n    },\n    clientLength: {\n      x: 'clientWidth',\n      y: 'clientHeight'\n    }\n  };\n  return domTreeShapes.some(shape => {\n    // Determine if we are going backward or forward.\n    let goingForward = current >= start;\n    if (anchor === 'top' || anchor === 'left') {\n      goingForward = !goingForward;\n    }\n    const axis = anchor === 'left' || anchor === 'right' ? 'x' : 'y';\n    const scrollPosition = Math.round(shape[axisProperties.scrollPosition[axis]]);\n    const areNotAtStart = scrollPosition > 0;\n    const areNotAtEnd = scrollPosition + shape[axisProperties.clientLength[axis]] < shape[axisProperties.scrollLength[axis]];\n    if (goingForward && areNotAtEnd || !goingForward && areNotAtStart) {\n      return true;\n    }\n    return false;\n  });\n}\nconst iOS = typeof navigator !== 'undefined' && /iPad|iPhone|iPod/.test(navigator.userAgent);\nconst SwipeableDrawer = /*#__PURE__*/React.forwardRef(function SwipeableDrawer(inProps, ref) {\n  const props = useDefaultProps({\n    name: 'MuiSwipeableDrawer',\n    props: inProps\n  });\n  const theme = useTheme();\n  const transitionDurationDefault = {\n    enter: theme.transitions.duration.enteringScreen,\n    exit: theme.transitions.duration.leavingScreen\n  };\n  const {\n      anchor = 'left',\n      disableBackdropTransition = false,\n      disableDiscovery = false,\n      disableSwipeToOpen = iOS,\n      hideBackdrop,\n      hysteresis = 0.52,\n      allowSwipeInChildren = false,\n      minFlingVelocity = 450,\n      ModalProps: {\n        BackdropProps\n      } = {},\n      onClose,\n      onOpen,\n      open = false,\n      PaperProps = {},\n      SwipeAreaProps,\n      swipeAreaWidth = 20,\n      transitionDuration = transitionDurationDefault,\n      variant = 'temporary' // Mobile first.\n    } = props,\n    ModalPropsProp = _objectWithoutPropertiesLoose(props.ModalProps, _excluded),\n    other = _objectWithoutPropertiesLoose(props, _excluded2);\n  const [maybeSwiping, setMaybeSwiping] = React.useState(false);\n  const swipeInstance = React.useRef({\n    isSwiping: null\n  });\n  const swipeAreaRef = React.useRef();\n  const backdropRef = React.useRef();\n  const paperRef = React.useRef();\n  const handleRef = useForkRef(PaperProps.ref, paperRef);\n  const touchDetected = React.useRef(false);\n\n  // Ref for transition duration based on / to match swipe speed\n  const calculatedDurationRef = React.useRef();\n\n  // Use a ref so the open value used is always up to date inside useCallback.\n  useEnhancedEffect(() => {\n    calculatedDurationRef.current = null;\n  }, [open]);\n  const setPosition = React.useCallback((translate, options = {}) => {\n    const {\n      mode = null,\n      changeTransition = true\n    } = options;\n    const anchorRtl = getAnchor(theme, anchor);\n    const rtlTranslateMultiplier = ['right', 'bottom'].indexOf(anchorRtl) !== -1 ? 1 : -1;\n    const horizontalSwipe = isHorizontal(anchor);\n    const transform = horizontalSwipe ? `translate(${rtlTranslateMultiplier * translate}px, 0)` : `translate(0, ${rtlTranslateMultiplier * translate}px)`;\n    const drawerStyle = paperRef.current.style;\n    drawerStyle.webkitTransform = transform;\n    drawerStyle.transform = transform;\n    let transition = '';\n    if (mode) {\n      transition = theme.transitions.create('all', getTransitionProps({\n        easing: undefined,\n        style: undefined,\n        timeout: transitionDuration\n      }, {\n        mode\n      }));\n    }\n    if (changeTransition) {\n      drawerStyle.webkitTransition = transition;\n      drawerStyle.transition = transition;\n    }\n    if (!disableBackdropTransition && !hideBackdrop) {\n      const backdropStyle = backdropRef.current.style;\n      backdropStyle.opacity = 1 - translate / getMaxTranslate(horizontalSwipe, paperRef.current);\n      if (changeTransition) {\n        backdropStyle.webkitTransition = transition;\n        backdropStyle.transition = transition;\n      }\n    }\n  }, [anchor, disableBackdropTransition, hideBackdrop, theme, transitionDuration]);\n  const handleBodyTouchEnd = useEventCallback(nativeEvent => {\n    if (!touchDetected.current) {\n      return;\n    }\n    claimedSwipeInstance = null;\n    touchDetected.current = false;\n    ReactDOM.flushSync(() => {\n      setMaybeSwiping(false);\n    });\n\n    // The swipe wasn't started.\n    if (!swipeInstance.current.isSwiping) {\n      swipeInstance.current.isSwiping = null;\n      return;\n    }\n    swipeInstance.current.isSwiping = null;\n    const anchorRtl = getAnchor(theme, anchor);\n    const horizontal = isHorizontal(anchor);\n    let current;\n    if (horizontal) {\n      current = calculateCurrentX(anchorRtl, nativeEvent.changedTouches, ownerDocument(nativeEvent.currentTarget));\n    } else {\n      current = calculateCurrentY(anchorRtl, nativeEvent.changedTouches, ownerWindow(nativeEvent.currentTarget));\n    }\n    const startLocation = horizontal ? swipeInstance.current.startX : swipeInstance.current.startY;\n    const maxTranslate = getMaxTranslate(horizontal, paperRef.current);\n    const currentTranslate = getTranslate(current, startLocation, open, maxTranslate);\n    const translateRatio = currentTranslate / maxTranslate;\n    if (Math.abs(swipeInstance.current.velocity) > minFlingVelocity) {\n      // Calculate transition duration to match swipe speed\n      calculatedDurationRef.current = Math.abs((maxTranslate - currentTranslate) / swipeInstance.current.velocity) * 1000;\n    }\n    if (open) {\n      if (swipeInstance.current.velocity > minFlingVelocity || translateRatio > hysteresis) {\n        onClose();\n      } else {\n        // Reset the position, the swipe was aborted.\n        setPosition(0, {\n          mode: 'exit'\n        });\n      }\n      return;\n    }\n    if (swipeInstance.current.velocity < -minFlingVelocity || 1 - translateRatio > hysteresis) {\n      onOpen();\n    } else {\n      // Reset the position, the swipe was aborted.\n      setPosition(getMaxTranslate(horizontal, paperRef.current), {\n        mode: 'enter'\n      });\n    }\n  });\n  const startMaybeSwiping = (force = false) => {\n    if (!maybeSwiping) {\n      // on Safari Mobile, if you want to be able to have the 'click' event fired on child elements, nothing in the DOM can be changed.\n      // this is because Safari Mobile will not fire any mouse events (still fires touch though) if the DOM changes during mousemove.\n      // so do this change on first touchmove instead of touchstart\n      if (force || !(disableDiscovery && allowSwipeInChildren)) {\n        ReactDOM.flushSync(() => {\n          setMaybeSwiping(true);\n        });\n      }\n      const horizontalSwipe = isHorizontal(anchor);\n      if (!open && paperRef.current) {\n        // The ref may be null when a parent component updates while swiping.\n        setPosition(getMaxTranslate(horizontalSwipe, paperRef.current) + (disableDiscovery ? 15 : -DRAG_STARTED_SIGNAL), {\n          changeTransition: false\n        });\n      }\n      swipeInstance.current.velocity = 0;\n      swipeInstance.current.lastTime = null;\n      swipeInstance.current.lastTranslate = null;\n      swipeInstance.current.paperHit = false;\n      touchDetected.current = true;\n    }\n  };\n  const handleBodyTouchMove = useEventCallback(nativeEvent => {\n    // the ref may be null when a parent component updates while swiping\n    if (!paperRef.current || !touchDetected.current) {\n      return;\n    }\n\n    // We are not supposed to handle this touch move because the swipe was started in a scrollable container in the drawer\n    if (claimedSwipeInstance !== null && claimedSwipeInstance !== swipeInstance.current) {\n      return;\n    }\n    startMaybeSwiping(true);\n    const anchorRtl = getAnchor(theme, anchor);\n    const horizontalSwipe = isHorizontal(anchor);\n    const currentX = calculateCurrentX(anchorRtl, nativeEvent.touches, ownerDocument(nativeEvent.currentTarget));\n    const currentY = calculateCurrentY(anchorRtl, nativeEvent.touches, ownerWindow(nativeEvent.currentTarget));\n    if (open && paperRef.current.contains(nativeEvent.target) && claimedSwipeInstance === null) {\n      const domTreeShapes = getDomTreeShapes(nativeEvent.target, paperRef.current);\n      const hasNativeHandler = computeHasNativeHandler({\n        domTreeShapes,\n        start: horizontalSwipe ? swipeInstance.current.startX : swipeInstance.current.startY,\n        current: horizontalSwipe ? currentX : currentY,\n        anchor\n      });\n      if (hasNativeHandler) {\n        claimedSwipeInstance = true;\n        return;\n      }\n      claimedSwipeInstance = swipeInstance.current;\n    }\n\n    // We don't know yet.\n    if (swipeInstance.current.isSwiping == null) {\n      const dx = Math.abs(currentX - swipeInstance.current.startX);\n      const dy = Math.abs(currentY - swipeInstance.current.startY);\n      const definitelySwiping = horizontalSwipe ? dx > dy && dx > UNCERTAINTY_THRESHOLD : dy > dx && dy > UNCERTAINTY_THRESHOLD;\n      if (definitelySwiping && nativeEvent.cancelable) {\n        nativeEvent.preventDefault();\n      }\n      if (definitelySwiping === true || (horizontalSwipe ? dy > UNCERTAINTY_THRESHOLD : dx > UNCERTAINTY_THRESHOLD)) {\n        swipeInstance.current.isSwiping = definitelySwiping;\n        if (!definitelySwiping) {\n          handleBodyTouchEnd(nativeEvent);\n          return;\n        }\n\n        // Shift the starting point.\n        swipeInstance.current.startX = currentX;\n        swipeInstance.current.startY = currentY;\n\n        // Compensate for the part of the drawer displayed on touch start.\n        if (!disableDiscovery && !open) {\n          if (horizontalSwipe) {\n            swipeInstance.current.startX -= DRAG_STARTED_SIGNAL;\n          } else {\n            swipeInstance.current.startY -= DRAG_STARTED_SIGNAL;\n          }\n        }\n      }\n    }\n    if (!swipeInstance.current.isSwiping) {\n      return;\n    }\n    const maxTranslate = getMaxTranslate(horizontalSwipe, paperRef.current);\n    let startLocation = horizontalSwipe ? swipeInstance.current.startX : swipeInstance.current.startY;\n    if (open && !swipeInstance.current.paperHit) {\n      startLocation = Math.min(startLocation, maxTranslate);\n    }\n    const translate = getTranslate(horizontalSwipe ? currentX : currentY, startLocation, open, maxTranslate);\n    if (open) {\n      if (!swipeInstance.current.paperHit) {\n        const paperHit = horizontalSwipe ? currentX < maxTranslate : currentY < maxTranslate;\n        if (paperHit) {\n          swipeInstance.current.paperHit = true;\n          swipeInstance.current.startX = currentX;\n          swipeInstance.current.startY = currentY;\n        } else {\n          return;\n        }\n      } else if (translate === 0) {\n        swipeInstance.current.startX = currentX;\n        swipeInstance.current.startY = currentY;\n      }\n    }\n    if (swipeInstance.current.lastTranslate === null) {\n      swipeInstance.current.lastTranslate = translate;\n      swipeInstance.current.lastTime = performance.now() + 1;\n    }\n    const velocity = (translate - swipeInstance.current.lastTranslate) / (performance.now() - swipeInstance.current.lastTime) * 1e3;\n\n    // Low Pass filter.\n    swipeInstance.current.velocity = swipeInstance.current.velocity * 0.4 + velocity * 0.6;\n    swipeInstance.current.lastTranslate = translate;\n    swipeInstance.current.lastTime = performance.now();\n\n    // We are swiping, let's prevent the scroll event on iOS.\n    if (nativeEvent.cancelable) {\n      nativeEvent.preventDefault();\n    }\n    setPosition(translate);\n  });\n  const handleBodyTouchStart = useEventCallback(nativeEvent => {\n    // We are not supposed to handle this touch move.\n    // Example of use case: ignore the event if there is a Slider.\n    if (nativeEvent.defaultPrevented) {\n      return;\n    }\n\n    // We can only have one node at the time claiming ownership for handling the swipe.\n    if (nativeEvent.defaultMuiPrevented) {\n      return;\n    }\n\n    // At least one element clogs the drawer interaction zone.\n    if (open && (hideBackdrop || !backdropRef.current.contains(nativeEvent.target)) && !paperRef.current.contains(nativeEvent.target)) {\n      return;\n    }\n    const anchorRtl = getAnchor(theme, anchor);\n    const horizontalSwipe = isHorizontal(anchor);\n    const currentX = calculateCurrentX(anchorRtl, nativeEvent.touches, ownerDocument(nativeEvent.currentTarget));\n    const currentY = calculateCurrentY(anchorRtl, nativeEvent.touches, ownerWindow(nativeEvent.currentTarget));\n    if (!open) {\n      var _paperRef$current;\n      // logic for if swipe should be ignored:\n      // if disableSwipeToOpen\n      // if target != swipeArea, and target is not a child of paper ref\n      // if is a child of paper ref, and `allowSwipeInChildren` does not allow it\n      if (disableSwipeToOpen || !(nativeEvent.target === swipeAreaRef.current || (_paperRef$current = paperRef.current) != null && _paperRef$current.contains(nativeEvent.target) && (typeof allowSwipeInChildren === 'function' ? allowSwipeInChildren(nativeEvent, swipeAreaRef.current, paperRef.current) : allowSwipeInChildren))) {\n        return;\n      }\n      if (horizontalSwipe) {\n        if (currentX > swipeAreaWidth) {\n          return;\n        }\n      } else if (currentY > swipeAreaWidth) {\n        return;\n      }\n    }\n    nativeEvent.defaultMuiPrevented = true;\n    claimedSwipeInstance = null;\n    swipeInstance.current.startX = currentX;\n    swipeInstance.current.startY = currentY;\n    startMaybeSwiping();\n  });\n  React.useEffect(() => {\n    if (variant === 'temporary') {\n      const doc = ownerDocument(paperRef.current);\n      doc.addEventListener('touchstart', handleBodyTouchStart);\n      // A blocking listener prevents Firefox's navbar to auto-hide on scroll.\n      // It only needs to prevent scrolling on the drawer's content when open.\n      // When closed, the overlay prevents scrolling.\n      doc.addEventListener('touchmove', handleBodyTouchMove, {\n        passive: !open\n      });\n      doc.addEventListener('touchend', handleBodyTouchEnd);\n      return () => {\n        doc.removeEventListener('touchstart', handleBodyTouchStart);\n        doc.removeEventListener('touchmove', handleBodyTouchMove, {\n          passive: !open\n        });\n        doc.removeEventListener('touchend', handleBodyTouchEnd);\n      };\n    }\n    return undefined;\n  }, [variant, open, handleBodyTouchStart, handleBodyTouchMove, handleBodyTouchEnd]);\n  React.useEffect(() => () => {\n    // We need to release the lock.\n    if (claimedSwipeInstance === swipeInstance.current) {\n      claimedSwipeInstance = null;\n    }\n  }, []);\n  React.useEffect(() => {\n    if (!open) {\n      setMaybeSwiping(false);\n    }\n  }, [open]);\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(Drawer, _extends({\n      open: variant === 'temporary' && maybeSwiping ? true : open,\n      variant: variant,\n      ModalProps: _extends({\n        BackdropProps: _extends({}, BackdropProps, {\n          ref: backdropRef\n        })\n      }, variant === 'temporary' && {\n        keepMounted: true\n      }, ModalPropsProp),\n      hideBackdrop: hideBackdrop,\n      PaperProps: _extends({}, PaperProps, {\n        style: _extends({\n          pointerEvents: variant === 'temporary' && !open && !allowSwipeInChildren ? 'none' : ''\n        }, PaperProps.style),\n        ref: handleRef\n      }),\n      anchor: anchor,\n      transitionDuration: calculatedDurationRef.current || transitionDuration,\n      onClose: onClose,\n      ref: ref\n    }, other)), !disableSwipeToOpen && variant === 'temporary' && /*#__PURE__*/_jsx(NoSsr, {\n      children: /*#__PURE__*/_jsx(SwipeArea, _extends({\n        anchor: anchor,\n        ref: swipeAreaRef,\n        width: swipeAreaWidth\n      }, SwipeAreaProps))\n    })]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? SwipeableDrawer.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * If set to true, the swipe event will open the drawer even if the user begins the swipe on one of the drawer's children.\n   * This can be useful in scenarios where the drawer is partially visible.\n   * You can customize it further with a callback that determines which children the user can drag over to open the drawer\n   * (for example, to ignore other elements that handle touch move events, like sliders).\n   *\n   * @param {TouchEvent} event The 'touchstart' event\n   * @param {HTMLDivElement} swipeArea The swipe area element\n   * @param {HTMLDivElement} paper The drawer's paper element\n   *\n   * @default false\n   */\n  allowSwipeInChildren: PropTypes.oneOfType([PropTypes.func, PropTypes.bool]),\n  /**\n   * @ignore\n   */\n  anchor: PropTypes.oneOf(['bottom', 'left', 'right', 'top']),\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Disable the backdrop transition.\n   * This can improve the FPS on low-end devices.\n   * @default false\n   */\n  disableBackdropTransition: PropTypes.bool,\n  /**\n   * If `true`, touching the screen near the edge of the drawer will not slide in the drawer a bit\n   * to promote accidental discovery of the swipe gesture.\n   * @default false\n   */\n  disableDiscovery: PropTypes.bool,\n  /**\n   * If `true`, swipe to open is disabled. This is useful in browsers where swiping triggers\n   * navigation actions. Swipe to open is disabled on iOS browsers by default.\n   * @default typeof navigator !== 'undefined' && /iPad|iPhone|iPod/.test(navigator.userAgent)\n   */\n  disableSwipeToOpen: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  hideBackdrop: PropTypes.bool,\n  /**\n   * Affects how far the drawer must be opened/closed to change its state.\n   * Specified as percent (0-1) of the width of the drawer\n   * @default 0.52\n   */\n  hysteresis: PropTypes.number,\n  /**\n   * Defines, from which (average) velocity on, the swipe is\n   * defined as complete although hysteresis isn't reached.\n   * Good threshold is between 250 - 1000 px/s\n   * @default 450\n   */\n  minFlingVelocity: PropTypes.number,\n  /**\n   * @ignore\n   */\n  ModalProps: PropTypes /* @typescript-to-proptypes-ignore */.shape({\n    BackdropProps: PropTypes.shape({\n      component: elementTypeAcceptingRef\n    })\n  }),\n  /**\n   * Callback fired when the component requests to be closed.\n   *\n   * @param {React.SyntheticEvent<{}>} event The event source of the callback.\n   */\n  onClose: PropTypes.func.isRequired,\n  /**\n   * Callback fired when the component requests to be opened.\n   *\n   * @param {React.SyntheticEvent<{}>} event The event source of the callback.\n   */\n  onOpen: PropTypes.func.isRequired,\n  /**\n   * If `true`, the component is shown.\n   * @default false\n   */\n  open: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  PaperProps: PropTypes /* @typescript-to-proptypes-ignore */.shape({\n    component: elementTypeAcceptingRef,\n    style: PropTypes.object\n  }),\n  /**\n   * The element is used to intercept the touch events on the edge.\n   */\n  SwipeAreaProps: PropTypes.object,\n  /**\n   * The width of the left most (or right most) area in `px` that\n   * the drawer can be swiped open from.\n   * @default 20\n   */\n  swipeAreaWidth: PropTypes.number,\n  /**\n   * The duration for the transition, in milliseconds.\n   * You may specify a single timeout for all transitions, or individually with an object.\n   * @default {\n   *   enter: theme.transitions.duration.enteringScreen,\n   *   exit: theme.transitions.duration.leavingScreen,\n   * }\n   */\n  transitionDuration: PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })]),\n  /**\n   * @ignore\n   */\n  variant: PropTypes.oneOf(['permanent', 'persistent', 'temporary'])\n} : void 0;\nexport default SwipeableDrawer;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "_excluded2", "React", "ReactDOM", "PropTypes", "elementTypeAcceptingRef", "NoSsr", "useDefaultProps", "Drawer", "getAnchor", "isHorizontal", "useForkRef", "ownerDocument", "ownerWindow", "useEventCallback", "useEnhancedEffect", "useTheme", "getTransitionProps", "SwipeArea", "jsx", "_jsx", "jsxs", "_jsxs", "UNCERTAINTY_THRESHOLD", "DRAG_STARTED_SIGNAL", "claimedSwipeInstance", "reset", "calculateCurrentX", "anchor", "touches", "doc", "body", "offsetWidth", "pageX", "calculateCurrentY", "containerWindow", "innerHeight", "clientY", "getMaxTranslate", "horizontalSwipe", "paperInstance", "clientWidth", "clientHeight", "getTranslate", "currentTranslate", "startLocation", "open", "maxTranslate", "Math", "min", "max", "getDomTreeShapes", "element", "rootNode", "domTreeShapes", "parentElement", "style", "getComputedStyle", "getPropertyValue", "scrollWidth", "scrollHeight", "push", "computeHasNativeHandler", "start", "current", "axisProperties", "scrollPosition", "x", "y", "<PERSON><PERSON><PERSON><PERSON>", "clientLength", "some", "shape", "goingForward", "axis", "round", "areNotAtStart", "areNotAtEnd", "iOS", "navigator", "test", "userAgent", "SwipeableDrawer", "forwardRef", "inProps", "ref", "props", "name", "theme", "transitionDurationDefault", "enter", "transitions", "duration", "enteringScreen", "exit", "leavingScreen", "disableBackdropTransition", "disableDiscovery", "disableSwipeToOpen", "hideBackdrop", "hysteresis", "allowSwipeInChildren", "minFlingVelocity", "ModalProps", "BackdropProps", "onClose", "onOpen", "PaperProps", "SwipeAreaProps", "swipe<PERSON><PERSON><PERSON><PERSON><PERSON>", "transitionDuration", "variant", "ModalPropsProp", "other", "maybeSwiping", "setMaybeSwiping", "useState", "swipeInstance", "useRef", "isSwiping", "swipeAreaRef", "backdropRef", "paperRef", "handleRef", "touchDetected", "calculatedDurationRef", "setPosition", "useCallback", "translate", "options", "mode", "changeTransition", "anchorRtl", "rtlTranslateMultiplier", "indexOf", "transform", "drawerStyle", "webkitTransform", "transition", "create", "easing", "undefined", "timeout", "webkitTransition", "backdropStyle", "opacity", "handleBodyTouchEnd", "nativeEvent", "flushSync", "horizontal", "changedTouches", "currentTarget", "startX", "startY", "translateRatio", "abs", "velocity", "startMaybeSwiping", "force", "lastTime", "lastTranslate", "paperHit", "handleBodyTouchMove", "currentX", "currentY", "contains", "target", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dx", "dy", "definitelySwiping", "cancelable", "preventDefault", "performance", "now", "handleBodyTouchStart", "defaultPrevented", "defaultMuiPrevented", "_paperRef$current", "useEffect", "addEventListener", "passive", "removeEventListener", "Fragment", "children", "keepMounted", "pointerEvents", "width", "process", "env", "NODE_ENV", "propTypes", "oneOfType", "func", "bool", "oneOf", "node", "number", "component", "isRequired", "object", "appear"], "sources": ["F:/my_work/game_logic_preview/frontend/node_modules/@mui/material/SwipeableDrawer/SwipeableDrawer.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"BackdropProps\"],\n  _excluded2 = [\"anchor\", \"disableBackdropTransition\", \"disableDiscovery\", \"disableSwipeToOpen\", \"hideBackdrop\", \"hysteresis\", \"allowSwipeInChildren\", \"minFlingVelocity\", \"ModalProps\", \"onClose\", \"onOpen\", \"open\", \"PaperProps\", \"SwipeAreaProps\", \"swipeAreaWidth\", \"transitionDuration\", \"variant\"];\nimport * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport PropTypes from 'prop-types';\nimport elementTypeAcceptingRef from '@mui/utils/elementTypeAcceptingRef';\nimport NoSsr from '../NoSsr';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport Drawer, { getAnchor, isHorizontal } from '../Drawer/Drawer';\nimport useForkRef from '../utils/useForkRef';\nimport ownerDocument from '../utils/ownerDocument';\nimport ownerWindow from '../utils/ownerWindow';\nimport useEventCallback from '../utils/useEventCallback';\nimport useEnhancedEffect from '../utils/useEnhancedEffect';\nimport useTheme from '../styles/useTheme';\nimport { getTransitionProps } from '../transitions/utils';\nimport SwipeArea from './SwipeArea';\n\n// This value is closed to what browsers are using internally to\n// trigger a native scroll.\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst UNCERTAINTY_THRESHOLD = 3; // px\n\n// This is the part of the drawer displayed on touch start.\nconst DRAG_STARTED_SIGNAL = 20; // px\n\n// We can only have one instance at the time claiming ownership for handling the swipe.\n// Otherwise, the UX would be confusing.\n// That's why we use a singleton here.\nlet claimedSwipeInstance = null;\n\n// Exported for test purposes.\nexport function reset() {\n  claimedSwipeInstance = null;\n}\nfunction calculateCurrentX(anchor, touches, doc) {\n  return anchor === 'right' ? doc.body.offsetWidth - touches[0].pageX : touches[0].pageX;\n}\nfunction calculateCurrentY(anchor, touches, containerWindow) {\n  return anchor === 'bottom' ? containerWindow.innerHeight - touches[0].clientY : touches[0].clientY;\n}\nfunction getMaxTranslate(horizontalSwipe, paperInstance) {\n  return horizontalSwipe ? paperInstance.clientWidth : paperInstance.clientHeight;\n}\nfunction getTranslate(currentTranslate, startLocation, open, maxTranslate) {\n  return Math.min(Math.max(open ? startLocation - currentTranslate : maxTranslate + startLocation - currentTranslate, 0), maxTranslate);\n}\n\n/**\n * @param {Element | null} element\n * @param {Element} rootNode\n */\nfunction getDomTreeShapes(element, rootNode) {\n  // Adapted from https://github.com/oliviertassinari/react-swipeable-views/blob/7666de1dba253b896911adf2790ce51467670856/packages/react-swipeable-views/src/SwipeableViews.js#L129\n  const domTreeShapes = [];\n  while (element && element !== rootNode.parentElement) {\n    const style = ownerWindow(rootNode).getComputedStyle(element);\n    if (\n    // Ignore the scroll children if the element is absolute positioned.\n    style.getPropertyValue('position') === 'absolute' ||\n    // Ignore the scroll children if the element has an overflowX hidden\n    style.getPropertyValue('overflow-x') === 'hidden') {\n      // noop\n    } else if (element.clientWidth > 0 && element.scrollWidth > element.clientWidth || element.clientHeight > 0 && element.scrollHeight > element.clientHeight) {\n      // Ignore the nodes that have no width.\n      // Keep elements with a scroll\n      domTreeShapes.push(element);\n    }\n    element = element.parentElement;\n  }\n  return domTreeShapes;\n}\n\n/**\n * @param {object} param0\n * @param {ReturnType<getDomTreeShapes>} param0.domTreeShapes\n */\nfunction computeHasNativeHandler({\n  domTreeShapes,\n  start,\n  current,\n  anchor\n}) {\n  // Adapted from https://github.com/oliviertassinari/react-swipeable-views/blob/7666de1dba253b896911adf2790ce51467670856/packages/react-swipeable-views/src/SwipeableViews.js#L175\n  const axisProperties = {\n    scrollPosition: {\n      x: 'scrollLeft',\n      y: 'scrollTop'\n    },\n    scrollLength: {\n      x: 'scrollWidth',\n      y: 'scrollHeight'\n    },\n    clientLength: {\n      x: 'clientWidth',\n      y: 'clientHeight'\n    }\n  };\n  return domTreeShapes.some(shape => {\n    // Determine if we are going backward or forward.\n    let goingForward = current >= start;\n    if (anchor === 'top' || anchor === 'left') {\n      goingForward = !goingForward;\n    }\n    const axis = anchor === 'left' || anchor === 'right' ? 'x' : 'y';\n    const scrollPosition = Math.round(shape[axisProperties.scrollPosition[axis]]);\n    const areNotAtStart = scrollPosition > 0;\n    const areNotAtEnd = scrollPosition + shape[axisProperties.clientLength[axis]] < shape[axisProperties.scrollLength[axis]];\n    if (goingForward && areNotAtEnd || !goingForward && areNotAtStart) {\n      return true;\n    }\n    return false;\n  });\n}\nconst iOS = typeof navigator !== 'undefined' && /iPad|iPhone|iPod/.test(navigator.userAgent);\nconst SwipeableDrawer = /*#__PURE__*/React.forwardRef(function SwipeableDrawer(inProps, ref) {\n  const props = useDefaultProps({\n    name: 'MuiSwipeableDrawer',\n    props: inProps\n  });\n  const theme = useTheme();\n  const transitionDurationDefault = {\n    enter: theme.transitions.duration.enteringScreen,\n    exit: theme.transitions.duration.leavingScreen\n  };\n  const {\n      anchor = 'left',\n      disableBackdropTransition = false,\n      disableDiscovery = false,\n      disableSwipeToOpen = iOS,\n      hideBackdrop,\n      hysteresis = 0.52,\n      allowSwipeInChildren = false,\n      minFlingVelocity = 450,\n      ModalProps: {\n        BackdropProps\n      } = {},\n      onClose,\n      onOpen,\n      open = false,\n      PaperProps = {},\n      SwipeAreaProps,\n      swipeAreaWidth = 20,\n      transitionDuration = transitionDurationDefault,\n      variant = 'temporary' // Mobile first.\n    } = props,\n    ModalPropsProp = _objectWithoutPropertiesLoose(props.ModalProps, _excluded),\n    other = _objectWithoutPropertiesLoose(props, _excluded2);\n  const [maybeSwiping, setMaybeSwiping] = React.useState(false);\n  const swipeInstance = React.useRef({\n    isSwiping: null\n  });\n  const swipeAreaRef = React.useRef();\n  const backdropRef = React.useRef();\n  const paperRef = React.useRef();\n  const handleRef = useForkRef(PaperProps.ref, paperRef);\n  const touchDetected = React.useRef(false);\n\n  // Ref for transition duration based on / to match swipe speed\n  const calculatedDurationRef = React.useRef();\n\n  // Use a ref so the open value used is always up to date inside useCallback.\n  useEnhancedEffect(() => {\n    calculatedDurationRef.current = null;\n  }, [open]);\n  const setPosition = React.useCallback((translate, options = {}) => {\n    const {\n      mode = null,\n      changeTransition = true\n    } = options;\n    const anchorRtl = getAnchor(theme, anchor);\n    const rtlTranslateMultiplier = ['right', 'bottom'].indexOf(anchorRtl) !== -1 ? 1 : -1;\n    const horizontalSwipe = isHorizontal(anchor);\n    const transform = horizontalSwipe ? `translate(${rtlTranslateMultiplier * translate}px, 0)` : `translate(0, ${rtlTranslateMultiplier * translate}px)`;\n    const drawerStyle = paperRef.current.style;\n    drawerStyle.webkitTransform = transform;\n    drawerStyle.transform = transform;\n    let transition = '';\n    if (mode) {\n      transition = theme.transitions.create('all', getTransitionProps({\n        easing: undefined,\n        style: undefined,\n        timeout: transitionDuration\n      }, {\n        mode\n      }));\n    }\n    if (changeTransition) {\n      drawerStyle.webkitTransition = transition;\n      drawerStyle.transition = transition;\n    }\n    if (!disableBackdropTransition && !hideBackdrop) {\n      const backdropStyle = backdropRef.current.style;\n      backdropStyle.opacity = 1 - translate / getMaxTranslate(horizontalSwipe, paperRef.current);\n      if (changeTransition) {\n        backdropStyle.webkitTransition = transition;\n        backdropStyle.transition = transition;\n      }\n    }\n  }, [anchor, disableBackdropTransition, hideBackdrop, theme, transitionDuration]);\n  const handleBodyTouchEnd = useEventCallback(nativeEvent => {\n    if (!touchDetected.current) {\n      return;\n    }\n    claimedSwipeInstance = null;\n    touchDetected.current = false;\n    ReactDOM.flushSync(() => {\n      setMaybeSwiping(false);\n    });\n\n    // The swipe wasn't started.\n    if (!swipeInstance.current.isSwiping) {\n      swipeInstance.current.isSwiping = null;\n      return;\n    }\n    swipeInstance.current.isSwiping = null;\n    const anchorRtl = getAnchor(theme, anchor);\n    const horizontal = isHorizontal(anchor);\n    let current;\n    if (horizontal) {\n      current = calculateCurrentX(anchorRtl, nativeEvent.changedTouches, ownerDocument(nativeEvent.currentTarget));\n    } else {\n      current = calculateCurrentY(anchorRtl, nativeEvent.changedTouches, ownerWindow(nativeEvent.currentTarget));\n    }\n    const startLocation = horizontal ? swipeInstance.current.startX : swipeInstance.current.startY;\n    const maxTranslate = getMaxTranslate(horizontal, paperRef.current);\n    const currentTranslate = getTranslate(current, startLocation, open, maxTranslate);\n    const translateRatio = currentTranslate / maxTranslate;\n    if (Math.abs(swipeInstance.current.velocity) > minFlingVelocity) {\n      // Calculate transition duration to match swipe speed\n      calculatedDurationRef.current = Math.abs((maxTranslate - currentTranslate) / swipeInstance.current.velocity) * 1000;\n    }\n    if (open) {\n      if (swipeInstance.current.velocity > minFlingVelocity || translateRatio > hysteresis) {\n        onClose();\n      } else {\n        // Reset the position, the swipe was aborted.\n        setPosition(0, {\n          mode: 'exit'\n        });\n      }\n      return;\n    }\n    if (swipeInstance.current.velocity < -minFlingVelocity || 1 - translateRatio > hysteresis) {\n      onOpen();\n    } else {\n      // Reset the position, the swipe was aborted.\n      setPosition(getMaxTranslate(horizontal, paperRef.current), {\n        mode: 'enter'\n      });\n    }\n  });\n  const startMaybeSwiping = (force = false) => {\n    if (!maybeSwiping) {\n      // on Safari Mobile, if you want to be able to have the 'click' event fired on child elements, nothing in the DOM can be changed.\n      // this is because Safari Mobile will not fire any mouse events (still fires touch though) if the DOM changes during mousemove.\n      // so do this change on first touchmove instead of touchstart\n      if (force || !(disableDiscovery && allowSwipeInChildren)) {\n        ReactDOM.flushSync(() => {\n          setMaybeSwiping(true);\n        });\n      }\n      const horizontalSwipe = isHorizontal(anchor);\n      if (!open && paperRef.current) {\n        // The ref may be null when a parent component updates while swiping.\n        setPosition(getMaxTranslate(horizontalSwipe, paperRef.current) + (disableDiscovery ? 15 : -DRAG_STARTED_SIGNAL), {\n          changeTransition: false\n        });\n      }\n      swipeInstance.current.velocity = 0;\n      swipeInstance.current.lastTime = null;\n      swipeInstance.current.lastTranslate = null;\n      swipeInstance.current.paperHit = false;\n      touchDetected.current = true;\n    }\n  };\n  const handleBodyTouchMove = useEventCallback(nativeEvent => {\n    // the ref may be null when a parent component updates while swiping\n    if (!paperRef.current || !touchDetected.current) {\n      return;\n    }\n\n    // We are not supposed to handle this touch move because the swipe was started in a scrollable container in the drawer\n    if (claimedSwipeInstance !== null && claimedSwipeInstance !== swipeInstance.current) {\n      return;\n    }\n    startMaybeSwiping(true);\n    const anchorRtl = getAnchor(theme, anchor);\n    const horizontalSwipe = isHorizontal(anchor);\n    const currentX = calculateCurrentX(anchorRtl, nativeEvent.touches, ownerDocument(nativeEvent.currentTarget));\n    const currentY = calculateCurrentY(anchorRtl, nativeEvent.touches, ownerWindow(nativeEvent.currentTarget));\n    if (open && paperRef.current.contains(nativeEvent.target) && claimedSwipeInstance === null) {\n      const domTreeShapes = getDomTreeShapes(nativeEvent.target, paperRef.current);\n      const hasNativeHandler = computeHasNativeHandler({\n        domTreeShapes,\n        start: horizontalSwipe ? swipeInstance.current.startX : swipeInstance.current.startY,\n        current: horizontalSwipe ? currentX : currentY,\n        anchor\n      });\n      if (hasNativeHandler) {\n        claimedSwipeInstance = true;\n        return;\n      }\n      claimedSwipeInstance = swipeInstance.current;\n    }\n\n    // We don't know yet.\n    if (swipeInstance.current.isSwiping == null) {\n      const dx = Math.abs(currentX - swipeInstance.current.startX);\n      const dy = Math.abs(currentY - swipeInstance.current.startY);\n      const definitelySwiping = horizontalSwipe ? dx > dy && dx > UNCERTAINTY_THRESHOLD : dy > dx && dy > UNCERTAINTY_THRESHOLD;\n      if (definitelySwiping && nativeEvent.cancelable) {\n        nativeEvent.preventDefault();\n      }\n      if (definitelySwiping === true || (horizontalSwipe ? dy > UNCERTAINTY_THRESHOLD : dx > UNCERTAINTY_THRESHOLD)) {\n        swipeInstance.current.isSwiping = definitelySwiping;\n        if (!definitelySwiping) {\n          handleBodyTouchEnd(nativeEvent);\n          return;\n        }\n\n        // Shift the starting point.\n        swipeInstance.current.startX = currentX;\n        swipeInstance.current.startY = currentY;\n\n        // Compensate for the part of the drawer displayed on touch start.\n        if (!disableDiscovery && !open) {\n          if (horizontalSwipe) {\n            swipeInstance.current.startX -= DRAG_STARTED_SIGNAL;\n          } else {\n            swipeInstance.current.startY -= DRAG_STARTED_SIGNAL;\n          }\n        }\n      }\n    }\n    if (!swipeInstance.current.isSwiping) {\n      return;\n    }\n    const maxTranslate = getMaxTranslate(horizontalSwipe, paperRef.current);\n    let startLocation = horizontalSwipe ? swipeInstance.current.startX : swipeInstance.current.startY;\n    if (open && !swipeInstance.current.paperHit) {\n      startLocation = Math.min(startLocation, maxTranslate);\n    }\n    const translate = getTranslate(horizontalSwipe ? currentX : currentY, startLocation, open, maxTranslate);\n    if (open) {\n      if (!swipeInstance.current.paperHit) {\n        const paperHit = horizontalSwipe ? currentX < maxTranslate : currentY < maxTranslate;\n        if (paperHit) {\n          swipeInstance.current.paperHit = true;\n          swipeInstance.current.startX = currentX;\n          swipeInstance.current.startY = currentY;\n        } else {\n          return;\n        }\n      } else if (translate === 0) {\n        swipeInstance.current.startX = currentX;\n        swipeInstance.current.startY = currentY;\n      }\n    }\n    if (swipeInstance.current.lastTranslate === null) {\n      swipeInstance.current.lastTranslate = translate;\n      swipeInstance.current.lastTime = performance.now() + 1;\n    }\n    const velocity = (translate - swipeInstance.current.lastTranslate) / (performance.now() - swipeInstance.current.lastTime) * 1e3;\n\n    // Low Pass filter.\n    swipeInstance.current.velocity = swipeInstance.current.velocity * 0.4 + velocity * 0.6;\n    swipeInstance.current.lastTranslate = translate;\n    swipeInstance.current.lastTime = performance.now();\n\n    // We are swiping, let's prevent the scroll event on iOS.\n    if (nativeEvent.cancelable) {\n      nativeEvent.preventDefault();\n    }\n    setPosition(translate);\n  });\n  const handleBodyTouchStart = useEventCallback(nativeEvent => {\n    // We are not supposed to handle this touch move.\n    // Example of use case: ignore the event if there is a Slider.\n    if (nativeEvent.defaultPrevented) {\n      return;\n    }\n\n    // We can only have one node at the time claiming ownership for handling the swipe.\n    if (nativeEvent.defaultMuiPrevented) {\n      return;\n    }\n\n    // At least one element clogs the drawer interaction zone.\n    if (open && (hideBackdrop || !backdropRef.current.contains(nativeEvent.target)) && !paperRef.current.contains(nativeEvent.target)) {\n      return;\n    }\n    const anchorRtl = getAnchor(theme, anchor);\n    const horizontalSwipe = isHorizontal(anchor);\n    const currentX = calculateCurrentX(anchorRtl, nativeEvent.touches, ownerDocument(nativeEvent.currentTarget));\n    const currentY = calculateCurrentY(anchorRtl, nativeEvent.touches, ownerWindow(nativeEvent.currentTarget));\n    if (!open) {\n      var _paperRef$current;\n      // logic for if swipe should be ignored:\n      // if disableSwipeToOpen\n      // if target != swipeArea, and target is not a child of paper ref\n      // if is a child of paper ref, and `allowSwipeInChildren` does not allow it\n      if (disableSwipeToOpen || !(nativeEvent.target === swipeAreaRef.current || (_paperRef$current = paperRef.current) != null && _paperRef$current.contains(nativeEvent.target) && (typeof allowSwipeInChildren === 'function' ? allowSwipeInChildren(nativeEvent, swipeAreaRef.current, paperRef.current) : allowSwipeInChildren))) {\n        return;\n      }\n      if (horizontalSwipe) {\n        if (currentX > swipeAreaWidth) {\n          return;\n        }\n      } else if (currentY > swipeAreaWidth) {\n        return;\n      }\n    }\n    nativeEvent.defaultMuiPrevented = true;\n    claimedSwipeInstance = null;\n    swipeInstance.current.startX = currentX;\n    swipeInstance.current.startY = currentY;\n    startMaybeSwiping();\n  });\n  React.useEffect(() => {\n    if (variant === 'temporary') {\n      const doc = ownerDocument(paperRef.current);\n      doc.addEventListener('touchstart', handleBodyTouchStart);\n      // A blocking listener prevents Firefox's navbar to auto-hide on scroll.\n      // It only needs to prevent scrolling on the drawer's content when open.\n      // When closed, the overlay prevents scrolling.\n      doc.addEventListener('touchmove', handleBodyTouchMove, {\n        passive: !open\n      });\n      doc.addEventListener('touchend', handleBodyTouchEnd);\n      return () => {\n        doc.removeEventListener('touchstart', handleBodyTouchStart);\n        doc.removeEventListener('touchmove', handleBodyTouchMove, {\n          passive: !open\n        });\n        doc.removeEventListener('touchend', handleBodyTouchEnd);\n      };\n    }\n    return undefined;\n  }, [variant, open, handleBodyTouchStart, handleBodyTouchMove, handleBodyTouchEnd]);\n  React.useEffect(() => () => {\n    // We need to release the lock.\n    if (claimedSwipeInstance === swipeInstance.current) {\n      claimedSwipeInstance = null;\n    }\n  }, []);\n  React.useEffect(() => {\n    if (!open) {\n      setMaybeSwiping(false);\n    }\n  }, [open]);\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(Drawer, _extends({\n      open: variant === 'temporary' && maybeSwiping ? true : open,\n      variant: variant,\n      ModalProps: _extends({\n        BackdropProps: _extends({}, BackdropProps, {\n          ref: backdropRef\n        })\n      }, variant === 'temporary' && {\n        keepMounted: true\n      }, ModalPropsProp),\n      hideBackdrop: hideBackdrop,\n      PaperProps: _extends({}, PaperProps, {\n        style: _extends({\n          pointerEvents: variant === 'temporary' && !open && !allowSwipeInChildren ? 'none' : ''\n        }, PaperProps.style),\n        ref: handleRef\n      }),\n      anchor: anchor,\n      transitionDuration: calculatedDurationRef.current || transitionDuration,\n      onClose: onClose,\n      ref: ref\n    }, other)), !disableSwipeToOpen && variant === 'temporary' && /*#__PURE__*/_jsx(NoSsr, {\n      children: /*#__PURE__*/_jsx(SwipeArea, _extends({\n        anchor: anchor,\n        ref: swipeAreaRef,\n        width: swipeAreaWidth\n      }, SwipeAreaProps))\n    })]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? SwipeableDrawer.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * If set to true, the swipe event will open the drawer even if the user begins the swipe on one of the drawer's children.\n   * This can be useful in scenarios where the drawer is partially visible.\n   * You can customize it further with a callback that determines which children the user can drag over to open the drawer\n   * (for example, to ignore other elements that handle touch move events, like sliders).\n   *\n   * @param {TouchEvent} event The 'touchstart' event\n   * @param {HTMLDivElement} swipeArea The swipe area element\n   * @param {HTMLDivElement} paper The drawer's paper element\n   *\n   * @default false\n   */\n  allowSwipeInChildren: PropTypes.oneOfType([PropTypes.func, PropTypes.bool]),\n  /**\n   * @ignore\n   */\n  anchor: PropTypes.oneOf(['bottom', 'left', 'right', 'top']),\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Disable the backdrop transition.\n   * This can improve the FPS on low-end devices.\n   * @default false\n   */\n  disableBackdropTransition: PropTypes.bool,\n  /**\n   * If `true`, touching the screen near the edge of the drawer will not slide in the drawer a bit\n   * to promote accidental discovery of the swipe gesture.\n   * @default false\n   */\n  disableDiscovery: PropTypes.bool,\n  /**\n   * If `true`, swipe to open is disabled. This is useful in browsers where swiping triggers\n   * navigation actions. Swipe to open is disabled on iOS browsers by default.\n   * @default typeof navigator !== 'undefined' && /iPad|iPhone|iPod/.test(navigator.userAgent)\n   */\n  disableSwipeToOpen: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  hideBackdrop: PropTypes.bool,\n  /**\n   * Affects how far the drawer must be opened/closed to change its state.\n   * Specified as percent (0-1) of the width of the drawer\n   * @default 0.52\n   */\n  hysteresis: PropTypes.number,\n  /**\n   * Defines, from which (average) velocity on, the swipe is\n   * defined as complete although hysteresis isn't reached.\n   * Good threshold is between 250 - 1000 px/s\n   * @default 450\n   */\n  minFlingVelocity: PropTypes.number,\n  /**\n   * @ignore\n   */\n  ModalProps: PropTypes /* @typescript-to-proptypes-ignore */.shape({\n    BackdropProps: PropTypes.shape({\n      component: elementTypeAcceptingRef\n    })\n  }),\n  /**\n   * Callback fired when the component requests to be closed.\n   *\n   * @param {React.SyntheticEvent<{}>} event The event source of the callback.\n   */\n  onClose: PropTypes.func.isRequired,\n  /**\n   * Callback fired when the component requests to be opened.\n   *\n   * @param {React.SyntheticEvent<{}>} event The event source of the callback.\n   */\n  onOpen: PropTypes.func.isRequired,\n  /**\n   * If `true`, the component is shown.\n   * @default false\n   */\n  open: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  PaperProps: PropTypes /* @typescript-to-proptypes-ignore */.shape({\n    component: elementTypeAcceptingRef,\n    style: PropTypes.object\n  }),\n  /**\n   * The element is used to intercept the touch events on the edge.\n   */\n  SwipeAreaProps: PropTypes.object,\n  /**\n   * The width of the left most (or right most) area in `px` that\n   * the drawer can be swiped open from.\n   * @default 20\n   */\n  swipeAreaWidth: PropTypes.number,\n  /**\n   * The duration for the transition, in milliseconds.\n   * You may specify a single timeout for all transitions, or individually with an object.\n   * @default {\n   *   enter: theme.transitions.duration.enteringScreen,\n   *   exit: theme.transitions.duration.leavingScreen,\n   * }\n   */\n  transitionDuration: PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })]),\n  /**\n   * @ignore\n   */\n  variant: PropTypes.oneOf(['permanent', 'persistent', 'temporary'])\n} : void 0;\nexport default SwipeableDrawer;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,eAAe,CAAC;EACjCC,UAAU,GAAG,CAAC,QAAQ,EAAE,2BAA2B,EAAE,kBAAkB,EAAE,oBAAoB,EAAE,cAAc,EAAE,YAAY,EAAE,sBAAsB,EAAE,kBAAkB,EAAE,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,YAAY,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,SAAS,CAAC;AACxS,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,QAAQ,MAAM,WAAW;AACrC,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,uBAAuB,MAAM,oCAAoC;AACxE,OAAOC,KAAK,MAAM,UAAU;AAC5B,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAOC,MAAM,IAAIC,SAAS,EAAEC,YAAY,QAAQ,kBAAkB;AAClE,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,aAAa,MAAM,wBAAwB;AAClD,OAAOC,WAAW,MAAM,sBAAsB;AAC9C,OAAOC,gBAAgB,MAAM,2BAA2B;AACxD,OAAOC,iBAAiB,MAAM,4BAA4B;AAC1D,OAAOC,QAAQ,MAAM,oBAAoB;AACzC,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,OAAOC,SAAS,MAAM,aAAa;;AAEnC;AACA;AACA,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,qBAAqB,GAAG,CAAC,CAAC,CAAC;;AAEjC;AACA,MAAMC,mBAAmB,GAAG,EAAE,CAAC,CAAC;;AAEhC;AACA;AACA;AACA,IAAIC,oBAAoB,GAAG,IAAI;;AAE/B;AACA,OAAO,SAASC,KAAKA,CAAA,EAAG;EACtBD,oBAAoB,GAAG,IAAI;AAC7B;AACA,SAASE,iBAAiBA,CAACC,MAAM,EAAEC,OAAO,EAAEC,GAAG,EAAE;EAC/C,OAAOF,MAAM,KAAK,OAAO,GAAGE,GAAG,CAACC,IAAI,CAACC,WAAW,GAAGH,OAAO,CAAC,CAAC,CAAC,CAACI,KAAK,GAAGJ,OAAO,CAAC,CAAC,CAAC,CAACI,KAAK;AACxF;AACA,SAASC,iBAAiBA,CAACN,MAAM,EAAEC,OAAO,EAAEM,eAAe,EAAE;EAC3D,OAAOP,MAAM,KAAK,QAAQ,GAAGO,eAAe,CAACC,WAAW,GAAGP,OAAO,CAAC,CAAC,CAAC,CAACQ,OAAO,GAAGR,OAAO,CAAC,CAAC,CAAC,CAACQ,OAAO;AACpG;AACA,SAASC,eAAeA,CAACC,eAAe,EAAEC,aAAa,EAAE;EACvD,OAAOD,eAAe,GAAGC,aAAa,CAACC,WAAW,GAAGD,aAAa,CAACE,YAAY;AACjF;AACA,SAASC,YAAYA,CAACC,gBAAgB,EAAEC,aAAa,EAAEC,IAAI,EAAEC,YAAY,EAAE;EACzE,OAAOC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACJ,IAAI,GAAGD,aAAa,GAAGD,gBAAgB,GAAGG,YAAY,GAAGF,aAAa,GAAGD,gBAAgB,EAAE,CAAC,CAAC,EAAEG,YAAY,CAAC;AACvI;;AAEA;AACA;AACA;AACA;AACA,SAASI,gBAAgBA,CAACC,OAAO,EAAEC,QAAQ,EAAE;EAC3C;EACA,MAAMC,aAAa,GAAG,EAAE;EACxB,OAAOF,OAAO,IAAIA,OAAO,KAAKC,QAAQ,CAACE,aAAa,EAAE;IACpD,MAAMC,KAAK,GAAG3C,WAAW,CAACwC,QAAQ,CAAC,CAACI,gBAAgB,CAACL,OAAO,CAAC;IAC7D;IACA;IACAI,KAAK,CAACE,gBAAgB,CAAC,UAAU,CAAC,KAAK,UAAU;IACjD;IACAF,KAAK,CAACE,gBAAgB,CAAC,YAAY,CAAC,KAAK,QAAQ,EAAE;MACjD;IAAA,CACD,MAAM,IAAIN,OAAO,CAACX,WAAW,GAAG,CAAC,IAAIW,OAAO,CAACO,WAAW,GAAGP,OAAO,CAACX,WAAW,IAAIW,OAAO,CAACV,YAAY,GAAG,CAAC,IAAIU,OAAO,CAACQ,YAAY,GAAGR,OAAO,CAACV,YAAY,EAAE;MAC1J;MACA;MACAY,aAAa,CAACO,IAAI,CAACT,OAAO,CAAC;IAC7B;IACAA,OAAO,GAAGA,OAAO,CAACG,aAAa;EACjC;EACA,OAAOD,aAAa;AACtB;;AAEA;AACA;AACA;AACA;AACA,SAASQ,uBAAuBA,CAAC;EAC/BR,aAAa;EACbS,KAAK;EACLC,OAAO;EACPpC;AACF,CAAC,EAAE;EACD;EACA,MAAMqC,cAAc,GAAG;IACrBC,cAAc,EAAE;MACdC,CAAC,EAAE,YAAY;MACfC,CAAC,EAAE;IACL,CAAC;IACDC,YAAY,EAAE;MACZF,CAAC,EAAE,aAAa;MAChBC,CAAC,EAAE;IACL,CAAC;IACDE,YAAY,EAAE;MACZH,CAAC,EAAE,aAAa;MAChBC,CAAC,EAAE;IACL;EACF,CAAC;EACD,OAAOd,aAAa,CAACiB,IAAI,CAACC,KAAK,IAAI;IACjC;IACA,IAAIC,YAAY,GAAGT,OAAO,IAAID,KAAK;IACnC,IAAInC,MAAM,KAAK,KAAK,IAAIA,MAAM,KAAK,MAAM,EAAE;MACzC6C,YAAY,GAAG,CAACA,YAAY;IAC9B;IACA,MAAMC,IAAI,GAAG9C,MAAM,KAAK,MAAM,IAAIA,MAAM,KAAK,OAAO,GAAG,GAAG,GAAG,GAAG;IAChE,MAAMsC,cAAc,GAAGlB,IAAI,CAAC2B,KAAK,CAACH,KAAK,CAACP,cAAc,CAACC,cAAc,CAACQ,IAAI,CAAC,CAAC,CAAC;IAC7E,MAAME,aAAa,GAAGV,cAAc,GAAG,CAAC;IACxC,MAAMW,WAAW,GAAGX,cAAc,GAAGM,KAAK,CAACP,cAAc,CAACK,YAAY,CAACI,IAAI,CAAC,CAAC,GAAGF,KAAK,CAACP,cAAc,CAACI,YAAY,CAACK,IAAI,CAAC,CAAC;IACxH,IAAID,YAAY,IAAII,WAAW,IAAI,CAACJ,YAAY,IAAIG,aAAa,EAAE;MACjE,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC,CAAC;AACJ;AACA,MAAME,GAAG,GAAG,OAAOC,SAAS,KAAK,WAAW,IAAI,kBAAkB,CAACC,IAAI,CAACD,SAAS,CAACE,SAAS,CAAC;AAC5F,MAAMC,eAAe,GAAG,aAAahF,KAAK,CAACiF,UAAU,CAAC,SAASD,eAAeA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC3F,MAAMC,KAAK,GAAG/E,eAAe,CAAC;IAC5BgF,IAAI,EAAE,oBAAoB;IAC1BD,KAAK,EAAEF;EACT,CAAC,CAAC;EACF,MAAMI,KAAK,GAAGxE,QAAQ,CAAC,CAAC;EACxB,MAAMyE,yBAAyB,GAAG;IAChCC,KAAK,EAAEF,KAAK,CAACG,WAAW,CAACC,QAAQ,CAACC,cAAc;IAChDC,IAAI,EAAEN,KAAK,CAACG,WAAW,CAACC,QAAQ,CAACG;EACnC,CAAC;EACD,MAAM;MACFnE,MAAM,GAAG,MAAM;MACfoE,yBAAyB,GAAG,KAAK;MACjCC,gBAAgB,GAAG,KAAK;MACxBC,kBAAkB,GAAGpB,GAAG;MACxBqB,YAAY;MACZC,UAAU,GAAG,IAAI;MACjBC,oBAAoB,GAAG,KAAK;MAC5BC,gBAAgB,GAAG,GAAG;MACtBC,UAAU,EAAE;QACVC;MACF,CAAC,GAAG,CAAC,CAAC;MACNC,OAAO;MACPC,MAAM;MACN5D,IAAI,GAAG,KAAK;MACZ6D,UAAU,GAAG,CAAC,CAAC;MACfC,cAAc;MACdC,cAAc,GAAG,EAAE;MACnBC,kBAAkB,GAAGrB,yBAAyB;MAC9CsB,OAAO,GAAG,WAAW,CAAC;IACxB,CAAC,GAAGzB,KAAK;IACT0B,cAAc,GAAGjH,6BAA6B,CAACuF,KAAK,CAACiB,UAAU,EAAEvG,SAAS,CAAC;IAC3EiH,KAAK,GAAGlH,6BAA6B,CAACuF,KAAK,EAAErF,UAAU,CAAC;EAC1D,MAAM,CAACiH,YAAY,EAAEC,eAAe,CAAC,GAAGjH,KAAK,CAACkH,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAMC,aAAa,GAAGnH,KAAK,CAACoH,MAAM,CAAC;IACjCC,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAMC,YAAY,GAAGtH,KAAK,CAACoH,MAAM,CAAC,CAAC;EACnC,MAAMG,WAAW,GAAGvH,KAAK,CAACoH,MAAM,CAAC,CAAC;EAClC,MAAMI,QAAQ,GAAGxH,KAAK,CAACoH,MAAM,CAAC,CAAC;EAC/B,MAAMK,SAAS,GAAGhH,UAAU,CAACgG,UAAU,CAACtB,GAAG,EAAEqC,QAAQ,CAAC;EACtD,MAAME,aAAa,GAAG1H,KAAK,CAACoH,MAAM,CAAC,KAAK,CAAC;;EAEzC;EACA,MAAMO,qBAAqB,GAAG3H,KAAK,CAACoH,MAAM,CAAC,CAAC;;EAE5C;EACAvG,iBAAiB,CAAC,MAAM;IACtB8G,qBAAqB,CAAC7D,OAAO,GAAG,IAAI;EACtC,CAAC,EAAE,CAAClB,IAAI,CAAC,CAAC;EACV,MAAMgF,WAAW,GAAG5H,KAAK,CAAC6H,WAAW,CAAC,CAACC,SAAS,EAAEC,OAAO,GAAG,CAAC,CAAC,KAAK;IACjE,MAAM;MACJC,IAAI,GAAG,IAAI;MACXC,gBAAgB,GAAG;IACrB,CAAC,GAAGF,OAAO;IACX,MAAMG,SAAS,GAAG3H,SAAS,CAAC+E,KAAK,EAAE5D,MAAM,CAAC;IAC1C,MAAMyG,sBAAsB,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC,CAACC,OAAO,CAACF,SAAS,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACrF,MAAM7F,eAAe,GAAG7B,YAAY,CAACkB,MAAM,CAAC;IAC5C,MAAM2G,SAAS,GAAGhG,eAAe,GAAG,aAAa8F,sBAAsB,GAAGL,SAAS,QAAQ,GAAG,gBAAgBK,sBAAsB,GAAGL,SAAS,KAAK;IACrJ,MAAMQ,WAAW,GAAGd,QAAQ,CAAC1D,OAAO,CAACR,KAAK;IAC1CgF,WAAW,CAACC,eAAe,GAAGF,SAAS;IACvCC,WAAW,CAACD,SAAS,GAAGA,SAAS;IACjC,IAAIG,UAAU,GAAG,EAAE;IACnB,IAAIR,IAAI,EAAE;MACRQ,UAAU,GAAGlD,KAAK,CAACG,WAAW,CAACgD,MAAM,CAAC,KAAK,EAAE1H,kBAAkB,CAAC;QAC9D2H,MAAM,EAAEC,SAAS;QACjBrF,KAAK,EAAEqF,SAAS;QAChBC,OAAO,EAAEhC;MACX,CAAC,EAAE;QACDoB;MACF,CAAC,CAAC,CAAC;IACL;IACA,IAAIC,gBAAgB,EAAE;MACpBK,WAAW,CAACO,gBAAgB,GAAGL,UAAU;MACzCF,WAAW,CAACE,UAAU,GAAGA,UAAU;IACrC;IACA,IAAI,CAAC1C,yBAAyB,IAAI,CAACG,YAAY,EAAE;MAC/C,MAAM6C,aAAa,GAAGvB,WAAW,CAACzD,OAAO,CAACR,KAAK;MAC/CwF,aAAa,CAACC,OAAO,GAAG,CAAC,GAAGjB,SAAS,GAAG1F,eAAe,CAACC,eAAe,EAAEmF,QAAQ,CAAC1D,OAAO,CAAC;MAC1F,IAAImE,gBAAgB,EAAE;QACpBa,aAAa,CAACD,gBAAgB,GAAGL,UAAU;QAC3CM,aAAa,CAACN,UAAU,GAAGA,UAAU;MACvC;IACF;EACF,CAAC,EAAE,CAAC9G,MAAM,EAAEoE,yBAAyB,EAAEG,YAAY,EAAEX,KAAK,EAAEsB,kBAAkB,CAAC,CAAC;EAChF,MAAMoC,kBAAkB,GAAGpI,gBAAgB,CAACqI,WAAW,IAAI;IACzD,IAAI,CAACvB,aAAa,CAAC5D,OAAO,EAAE;MAC1B;IACF;IACAvC,oBAAoB,GAAG,IAAI;IAC3BmG,aAAa,CAAC5D,OAAO,GAAG,KAAK;IAC7B7D,QAAQ,CAACiJ,SAAS,CAAC,MAAM;MACvBjC,eAAe,CAAC,KAAK,CAAC;IACxB,CAAC,CAAC;;IAEF;IACA,IAAI,CAACE,aAAa,CAACrD,OAAO,CAACuD,SAAS,EAAE;MACpCF,aAAa,CAACrD,OAAO,CAACuD,SAAS,GAAG,IAAI;MACtC;IACF;IACAF,aAAa,CAACrD,OAAO,CAACuD,SAAS,GAAG,IAAI;IACtC,MAAMa,SAAS,GAAG3H,SAAS,CAAC+E,KAAK,EAAE5D,MAAM,CAAC;IAC1C,MAAMyH,UAAU,GAAG3I,YAAY,CAACkB,MAAM,CAAC;IACvC,IAAIoC,OAAO;IACX,IAAIqF,UAAU,EAAE;MACdrF,OAAO,GAAGrC,iBAAiB,CAACyG,SAAS,EAAEe,WAAW,CAACG,cAAc,EAAE1I,aAAa,CAACuI,WAAW,CAACI,aAAa,CAAC,CAAC;IAC9G,CAAC,MAAM;MACLvF,OAAO,GAAG9B,iBAAiB,CAACkG,SAAS,EAAEe,WAAW,CAACG,cAAc,EAAEzI,WAAW,CAACsI,WAAW,CAACI,aAAa,CAAC,CAAC;IAC5G;IACA,MAAM1G,aAAa,GAAGwG,UAAU,GAAGhC,aAAa,CAACrD,OAAO,CAACwF,MAAM,GAAGnC,aAAa,CAACrD,OAAO,CAACyF,MAAM;IAC9F,MAAM1G,YAAY,GAAGT,eAAe,CAAC+G,UAAU,EAAE3B,QAAQ,CAAC1D,OAAO,CAAC;IAClE,MAAMpB,gBAAgB,GAAGD,YAAY,CAACqB,OAAO,EAAEnB,aAAa,EAAEC,IAAI,EAAEC,YAAY,CAAC;IACjF,MAAM2G,cAAc,GAAG9G,gBAAgB,GAAGG,YAAY;IACtD,IAAIC,IAAI,CAAC2G,GAAG,CAACtC,aAAa,CAACrD,OAAO,CAAC4F,QAAQ,CAAC,GAAGtD,gBAAgB,EAAE;MAC/D;MACAuB,qBAAqB,CAAC7D,OAAO,GAAGhB,IAAI,CAAC2G,GAAG,CAAC,CAAC5G,YAAY,GAAGH,gBAAgB,IAAIyE,aAAa,CAACrD,OAAO,CAAC4F,QAAQ,CAAC,GAAG,IAAI;IACrH;IACA,IAAI9G,IAAI,EAAE;MACR,IAAIuE,aAAa,CAACrD,OAAO,CAAC4F,QAAQ,GAAGtD,gBAAgB,IAAIoD,cAAc,GAAGtD,UAAU,EAAE;QACpFK,OAAO,CAAC,CAAC;MACX,CAAC,MAAM;QACL;QACAqB,WAAW,CAAC,CAAC,EAAE;UACbI,IAAI,EAAE;QACR,CAAC,CAAC;MACJ;MACA;IACF;IACA,IAAIb,aAAa,CAACrD,OAAO,CAAC4F,QAAQ,GAAG,CAACtD,gBAAgB,IAAI,CAAC,GAAGoD,cAAc,GAAGtD,UAAU,EAAE;MACzFM,MAAM,CAAC,CAAC;IACV,CAAC,MAAM;MACL;MACAoB,WAAW,CAACxF,eAAe,CAAC+G,UAAU,EAAE3B,QAAQ,CAAC1D,OAAO,CAAC,EAAE;QACzDkE,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF,MAAM2B,iBAAiB,GAAGA,CAACC,KAAK,GAAG,KAAK,KAAK;IAC3C,IAAI,CAAC5C,YAAY,EAAE;MACjB;MACA;MACA;MACA,IAAI4C,KAAK,IAAI,EAAE7D,gBAAgB,IAAII,oBAAoB,CAAC,EAAE;QACxDlG,QAAQ,CAACiJ,SAAS,CAAC,MAAM;UACvBjC,eAAe,CAAC,IAAI,CAAC;QACvB,CAAC,CAAC;MACJ;MACA,MAAM5E,eAAe,GAAG7B,YAAY,CAACkB,MAAM,CAAC;MAC5C,IAAI,CAACkB,IAAI,IAAI4E,QAAQ,CAAC1D,OAAO,EAAE;QAC7B;QACA8D,WAAW,CAACxF,eAAe,CAACC,eAAe,EAAEmF,QAAQ,CAAC1D,OAAO,CAAC,IAAIiC,gBAAgB,GAAG,EAAE,GAAG,CAACzE,mBAAmB,CAAC,EAAE;UAC/G2G,gBAAgB,EAAE;QACpB,CAAC,CAAC;MACJ;MACAd,aAAa,CAACrD,OAAO,CAAC4F,QAAQ,GAAG,CAAC;MAClCvC,aAAa,CAACrD,OAAO,CAAC+F,QAAQ,GAAG,IAAI;MACrC1C,aAAa,CAACrD,OAAO,CAACgG,aAAa,GAAG,IAAI;MAC1C3C,aAAa,CAACrD,OAAO,CAACiG,QAAQ,GAAG,KAAK;MACtCrC,aAAa,CAAC5D,OAAO,GAAG,IAAI;IAC9B;EACF,CAAC;EACD,MAAMkG,mBAAmB,GAAGpJ,gBAAgB,CAACqI,WAAW,IAAI;IAC1D;IACA,IAAI,CAACzB,QAAQ,CAAC1D,OAAO,IAAI,CAAC4D,aAAa,CAAC5D,OAAO,EAAE;MAC/C;IACF;;IAEA;IACA,IAAIvC,oBAAoB,KAAK,IAAI,IAAIA,oBAAoB,KAAK4F,aAAa,CAACrD,OAAO,EAAE;MACnF;IACF;IACA6F,iBAAiB,CAAC,IAAI,CAAC;IACvB,MAAMzB,SAAS,GAAG3H,SAAS,CAAC+E,KAAK,EAAE5D,MAAM,CAAC;IAC1C,MAAMW,eAAe,GAAG7B,YAAY,CAACkB,MAAM,CAAC;IAC5C,MAAMuI,QAAQ,GAAGxI,iBAAiB,CAACyG,SAAS,EAAEe,WAAW,CAACtH,OAAO,EAAEjB,aAAa,CAACuI,WAAW,CAACI,aAAa,CAAC,CAAC;IAC5G,MAAMa,QAAQ,GAAGlI,iBAAiB,CAACkG,SAAS,EAAEe,WAAW,CAACtH,OAAO,EAAEhB,WAAW,CAACsI,WAAW,CAACI,aAAa,CAAC,CAAC;IAC1G,IAAIzG,IAAI,IAAI4E,QAAQ,CAAC1D,OAAO,CAACqG,QAAQ,CAAClB,WAAW,CAACmB,MAAM,CAAC,IAAI7I,oBAAoB,KAAK,IAAI,EAAE;MAC1F,MAAM6B,aAAa,GAAGH,gBAAgB,CAACgG,WAAW,CAACmB,MAAM,EAAE5C,QAAQ,CAAC1D,OAAO,CAAC;MAC5E,MAAMuG,gBAAgB,GAAGzG,uBAAuB,CAAC;QAC/CR,aAAa;QACbS,KAAK,EAAExB,eAAe,GAAG8E,aAAa,CAACrD,OAAO,CAACwF,MAAM,GAAGnC,aAAa,CAACrD,OAAO,CAACyF,MAAM;QACpFzF,OAAO,EAAEzB,eAAe,GAAG4H,QAAQ,GAAGC,QAAQ;QAC9CxI;MACF,CAAC,CAAC;MACF,IAAI2I,gBAAgB,EAAE;QACpB9I,oBAAoB,GAAG,IAAI;QAC3B;MACF;MACAA,oBAAoB,GAAG4F,aAAa,CAACrD,OAAO;IAC9C;;IAEA;IACA,IAAIqD,aAAa,CAACrD,OAAO,CAACuD,SAAS,IAAI,IAAI,EAAE;MAC3C,MAAMiD,EAAE,GAAGxH,IAAI,CAAC2G,GAAG,CAACQ,QAAQ,GAAG9C,aAAa,CAACrD,OAAO,CAACwF,MAAM,CAAC;MAC5D,MAAMiB,EAAE,GAAGzH,IAAI,CAAC2G,GAAG,CAACS,QAAQ,GAAG/C,aAAa,CAACrD,OAAO,CAACyF,MAAM,CAAC;MAC5D,MAAMiB,iBAAiB,GAAGnI,eAAe,GAAGiI,EAAE,GAAGC,EAAE,IAAID,EAAE,GAAGjJ,qBAAqB,GAAGkJ,EAAE,GAAGD,EAAE,IAAIC,EAAE,GAAGlJ,qBAAqB;MACzH,IAAImJ,iBAAiB,IAAIvB,WAAW,CAACwB,UAAU,EAAE;QAC/CxB,WAAW,CAACyB,cAAc,CAAC,CAAC;MAC9B;MACA,IAAIF,iBAAiB,KAAK,IAAI,KAAKnI,eAAe,GAAGkI,EAAE,GAAGlJ,qBAAqB,GAAGiJ,EAAE,GAAGjJ,qBAAqB,CAAC,EAAE;QAC7G8F,aAAa,CAACrD,OAAO,CAACuD,SAAS,GAAGmD,iBAAiB;QACnD,IAAI,CAACA,iBAAiB,EAAE;UACtBxB,kBAAkB,CAACC,WAAW,CAAC;UAC/B;QACF;;QAEA;QACA9B,aAAa,CAACrD,OAAO,CAACwF,MAAM,GAAGW,QAAQ;QACvC9C,aAAa,CAACrD,OAAO,CAACyF,MAAM,GAAGW,QAAQ;;QAEvC;QACA,IAAI,CAACnE,gBAAgB,IAAI,CAACnD,IAAI,EAAE;UAC9B,IAAIP,eAAe,EAAE;YACnB8E,aAAa,CAACrD,OAAO,CAACwF,MAAM,IAAIhI,mBAAmB;UACrD,CAAC,MAAM;YACL6F,aAAa,CAACrD,OAAO,CAACyF,MAAM,IAAIjI,mBAAmB;UACrD;QACF;MACF;IACF;IACA,IAAI,CAAC6F,aAAa,CAACrD,OAAO,CAACuD,SAAS,EAAE;MACpC;IACF;IACA,MAAMxE,YAAY,GAAGT,eAAe,CAACC,eAAe,EAAEmF,QAAQ,CAAC1D,OAAO,CAAC;IACvE,IAAInB,aAAa,GAAGN,eAAe,GAAG8E,aAAa,CAACrD,OAAO,CAACwF,MAAM,GAAGnC,aAAa,CAACrD,OAAO,CAACyF,MAAM;IACjG,IAAI3G,IAAI,IAAI,CAACuE,aAAa,CAACrD,OAAO,CAACiG,QAAQ,EAAE;MAC3CpH,aAAa,GAAGG,IAAI,CAACC,GAAG,CAACJ,aAAa,EAAEE,YAAY,CAAC;IACvD;IACA,MAAMiF,SAAS,GAAGrF,YAAY,CAACJ,eAAe,GAAG4H,QAAQ,GAAGC,QAAQ,EAAEvH,aAAa,EAAEC,IAAI,EAAEC,YAAY,CAAC;IACxG,IAAID,IAAI,EAAE;MACR,IAAI,CAACuE,aAAa,CAACrD,OAAO,CAACiG,QAAQ,EAAE;QACnC,MAAMA,QAAQ,GAAG1H,eAAe,GAAG4H,QAAQ,GAAGpH,YAAY,GAAGqH,QAAQ,GAAGrH,YAAY;QACpF,IAAIkH,QAAQ,EAAE;UACZ5C,aAAa,CAACrD,OAAO,CAACiG,QAAQ,GAAG,IAAI;UACrC5C,aAAa,CAACrD,OAAO,CAACwF,MAAM,GAAGW,QAAQ;UACvC9C,aAAa,CAACrD,OAAO,CAACyF,MAAM,GAAGW,QAAQ;QACzC,CAAC,MAAM;UACL;QACF;MACF,CAAC,MAAM,IAAIpC,SAAS,KAAK,CAAC,EAAE;QAC1BX,aAAa,CAACrD,OAAO,CAACwF,MAAM,GAAGW,QAAQ;QACvC9C,aAAa,CAACrD,OAAO,CAACyF,MAAM,GAAGW,QAAQ;MACzC;IACF;IACA,IAAI/C,aAAa,CAACrD,OAAO,CAACgG,aAAa,KAAK,IAAI,EAAE;MAChD3C,aAAa,CAACrD,OAAO,CAACgG,aAAa,GAAGhC,SAAS;MAC/CX,aAAa,CAACrD,OAAO,CAAC+F,QAAQ,GAAGc,WAAW,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;IACxD;IACA,MAAMlB,QAAQ,GAAG,CAAC5B,SAAS,GAAGX,aAAa,CAACrD,OAAO,CAACgG,aAAa,KAAKa,WAAW,CAACC,GAAG,CAAC,CAAC,GAAGzD,aAAa,CAACrD,OAAO,CAAC+F,QAAQ,CAAC,GAAG,GAAG;;IAE/H;IACA1C,aAAa,CAACrD,OAAO,CAAC4F,QAAQ,GAAGvC,aAAa,CAACrD,OAAO,CAAC4F,QAAQ,GAAG,GAAG,GAAGA,QAAQ,GAAG,GAAG;IACtFvC,aAAa,CAACrD,OAAO,CAACgG,aAAa,GAAGhC,SAAS;IAC/CX,aAAa,CAACrD,OAAO,CAAC+F,QAAQ,GAAGc,WAAW,CAACC,GAAG,CAAC,CAAC;;IAElD;IACA,IAAI3B,WAAW,CAACwB,UAAU,EAAE;MAC1BxB,WAAW,CAACyB,cAAc,CAAC,CAAC;IAC9B;IACA9C,WAAW,CAACE,SAAS,CAAC;EACxB,CAAC,CAAC;EACF,MAAM+C,oBAAoB,GAAGjK,gBAAgB,CAACqI,WAAW,IAAI;IAC3D;IACA;IACA,IAAIA,WAAW,CAAC6B,gBAAgB,EAAE;MAChC;IACF;;IAEA;IACA,IAAI7B,WAAW,CAAC8B,mBAAmB,EAAE;MACnC;IACF;;IAEA;IACA,IAAInI,IAAI,KAAKqD,YAAY,IAAI,CAACsB,WAAW,CAACzD,OAAO,CAACqG,QAAQ,CAAClB,WAAW,CAACmB,MAAM,CAAC,CAAC,IAAI,CAAC5C,QAAQ,CAAC1D,OAAO,CAACqG,QAAQ,CAAClB,WAAW,CAACmB,MAAM,CAAC,EAAE;MACjI;IACF;IACA,MAAMlC,SAAS,GAAG3H,SAAS,CAAC+E,KAAK,EAAE5D,MAAM,CAAC;IAC1C,MAAMW,eAAe,GAAG7B,YAAY,CAACkB,MAAM,CAAC;IAC5C,MAAMuI,QAAQ,GAAGxI,iBAAiB,CAACyG,SAAS,EAAEe,WAAW,CAACtH,OAAO,EAAEjB,aAAa,CAACuI,WAAW,CAACI,aAAa,CAAC,CAAC;IAC5G,MAAMa,QAAQ,GAAGlI,iBAAiB,CAACkG,SAAS,EAAEe,WAAW,CAACtH,OAAO,EAAEhB,WAAW,CAACsI,WAAW,CAACI,aAAa,CAAC,CAAC;IAC1G,IAAI,CAACzG,IAAI,EAAE;MACT,IAAIoI,iBAAiB;MACrB;MACA;MACA;MACA;MACA,IAAIhF,kBAAkB,IAAI,EAAEiD,WAAW,CAACmB,MAAM,KAAK9C,YAAY,CAACxD,OAAO,IAAI,CAACkH,iBAAiB,GAAGxD,QAAQ,CAAC1D,OAAO,KAAK,IAAI,IAAIkH,iBAAiB,CAACb,QAAQ,CAAClB,WAAW,CAACmB,MAAM,CAAC,KAAK,OAAOjE,oBAAoB,KAAK,UAAU,GAAGA,oBAAoB,CAAC8C,WAAW,EAAE3B,YAAY,CAACxD,OAAO,EAAE0D,QAAQ,CAAC1D,OAAO,CAAC,GAAGqC,oBAAoB,CAAC,CAAC,EAAE;QAC/T;MACF;MACA,IAAI9D,eAAe,EAAE;QACnB,IAAI4H,QAAQ,GAAGtD,cAAc,EAAE;UAC7B;QACF;MACF,CAAC,MAAM,IAAIuD,QAAQ,GAAGvD,cAAc,EAAE;QACpC;MACF;IACF;IACAsC,WAAW,CAAC8B,mBAAmB,GAAG,IAAI;IACtCxJ,oBAAoB,GAAG,IAAI;IAC3B4F,aAAa,CAACrD,OAAO,CAACwF,MAAM,GAAGW,QAAQ;IACvC9C,aAAa,CAACrD,OAAO,CAACyF,MAAM,GAAGW,QAAQ;IACvCP,iBAAiB,CAAC,CAAC;EACrB,CAAC,CAAC;EACF3J,KAAK,CAACiL,SAAS,CAAC,MAAM;IACpB,IAAIpE,OAAO,KAAK,WAAW,EAAE;MAC3B,MAAMjF,GAAG,GAAGlB,aAAa,CAAC8G,QAAQ,CAAC1D,OAAO,CAAC;MAC3ClC,GAAG,CAACsJ,gBAAgB,CAAC,YAAY,EAAEL,oBAAoB,CAAC;MACxD;MACA;MACA;MACAjJ,GAAG,CAACsJ,gBAAgB,CAAC,WAAW,EAAElB,mBAAmB,EAAE;QACrDmB,OAAO,EAAE,CAACvI;MACZ,CAAC,CAAC;MACFhB,GAAG,CAACsJ,gBAAgB,CAAC,UAAU,EAAElC,kBAAkB,CAAC;MACpD,OAAO,MAAM;QACXpH,GAAG,CAACwJ,mBAAmB,CAAC,YAAY,EAAEP,oBAAoB,CAAC;QAC3DjJ,GAAG,CAACwJ,mBAAmB,CAAC,WAAW,EAAEpB,mBAAmB,EAAE;UACxDmB,OAAO,EAAE,CAACvI;QACZ,CAAC,CAAC;QACFhB,GAAG,CAACwJ,mBAAmB,CAAC,UAAU,EAAEpC,kBAAkB,CAAC;MACzD,CAAC;IACH;IACA,OAAOL,SAAS;EAClB,CAAC,EAAE,CAAC9B,OAAO,EAAEjE,IAAI,EAAEiI,oBAAoB,EAAEb,mBAAmB,EAAEhB,kBAAkB,CAAC,CAAC;EAClFhJ,KAAK,CAACiL,SAAS,CAAC,MAAM,MAAM;IAC1B;IACA,IAAI1J,oBAAoB,KAAK4F,aAAa,CAACrD,OAAO,EAAE;MAClDvC,oBAAoB,GAAG,IAAI;IAC7B;EACF,CAAC,EAAE,EAAE,CAAC;EACNvB,KAAK,CAACiL,SAAS,CAAC,MAAM;IACpB,IAAI,CAACrI,IAAI,EAAE;MACTqE,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC,EAAE,CAACrE,IAAI,CAAC,CAAC;EACV,OAAO,aAAaxB,KAAK,CAACpB,KAAK,CAACqL,QAAQ,EAAE;IACxCC,QAAQ,EAAE,CAAC,aAAapK,IAAI,CAACZ,MAAM,EAAEV,QAAQ,CAAC;MAC5CgD,IAAI,EAAEiE,OAAO,KAAK,WAAW,IAAIG,YAAY,GAAG,IAAI,GAAGpE,IAAI;MAC3DiE,OAAO,EAAEA,OAAO;MAChBR,UAAU,EAAEzG,QAAQ,CAAC;QACnB0G,aAAa,EAAE1G,QAAQ,CAAC,CAAC,CAAC,EAAE0G,aAAa,EAAE;UACzCnB,GAAG,EAAEoC;QACP,CAAC;MACH,CAAC,EAAEV,OAAO,KAAK,WAAW,IAAI;QAC5B0E,WAAW,EAAE;MACf,CAAC,EAAEzE,cAAc,CAAC;MAClBb,YAAY,EAAEA,YAAY;MAC1BQ,UAAU,EAAE7G,QAAQ,CAAC,CAAC,CAAC,EAAE6G,UAAU,EAAE;QACnCnD,KAAK,EAAE1D,QAAQ,CAAC;UACd4L,aAAa,EAAE3E,OAAO,KAAK,WAAW,IAAI,CAACjE,IAAI,IAAI,CAACuD,oBAAoB,GAAG,MAAM,GAAG;QACtF,CAAC,EAAEM,UAAU,CAACnD,KAAK,CAAC;QACpB6B,GAAG,EAAEsC;MACP,CAAC,CAAC;MACF/F,MAAM,EAAEA,MAAM;MACdkF,kBAAkB,EAAEe,qBAAqB,CAAC7D,OAAO,IAAI8C,kBAAkB;MACvEL,OAAO,EAAEA,OAAO;MAChBpB,GAAG,EAAEA;IACP,CAAC,EAAE4B,KAAK,CAAC,CAAC,EAAE,CAACf,kBAAkB,IAAIa,OAAO,KAAK,WAAW,IAAI,aAAa3F,IAAI,CAACd,KAAK,EAAE;MACrFkL,QAAQ,EAAE,aAAapK,IAAI,CAACF,SAAS,EAAEpB,QAAQ,CAAC;QAC9C8B,MAAM,EAAEA,MAAM;QACdyD,GAAG,EAAEmC,YAAY;QACjBmE,KAAK,EAAE9E;MACT,CAAC,EAAED,cAAc,CAAC;IACpB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AACFgF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG5G,eAAe,CAAC6G,SAAS,CAAC,yBAAyB;EACzF;EACA;EACA;EACA;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE1F,oBAAoB,EAAEjG,SAAS,CAAC4L,SAAS,CAAC,CAAC5L,SAAS,CAAC6L,IAAI,EAAE7L,SAAS,CAAC8L,IAAI,CAAC,CAAC;EAC3E;AACF;AACA;EACEtK,MAAM,EAAExB,SAAS,CAAC+L,KAAK,CAAC,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;EAC3D;AACF;AACA;EACEX,QAAQ,EAAEpL,SAAS,CAACgM,IAAI;EACxB;AACF;AACA;AACA;AACA;EACEpG,yBAAyB,EAAE5F,SAAS,CAAC8L,IAAI;EACzC;AACF;AACA;AACA;AACA;EACEjG,gBAAgB,EAAE7F,SAAS,CAAC8L,IAAI;EAChC;AACF;AACA;AACA;AACA;EACEhG,kBAAkB,EAAE9F,SAAS,CAAC8L,IAAI;EAClC;AACF;AACA;EACE/F,YAAY,EAAE/F,SAAS,CAAC8L,IAAI;EAC5B;AACF;AACA;AACA;AACA;EACE9F,UAAU,EAAEhG,SAAS,CAACiM,MAAM;EAC5B;AACF;AACA;AACA;AACA;AACA;EACE/F,gBAAgB,EAAElG,SAAS,CAACiM,MAAM;EAClC;AACF;AACA;EACE9F,UAAU,EAAEnG,SAAS,CAAC,sCAAsCoE,KAAK,CAAC;IAChEgC,aAAa,EAAEpG,SAAS,CAACoE,KAAK,CAAC;MAC7B8H,SAAS,EAAEjM;IACb,CAAC;EACH,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;EACEoG,OAAO,EAAErG,SAAS,CAAC6L,IAAI,CAACM,UAAU;EAClC;AACF;AACA;AACA;AACA;EACE7F,MAAM,EAAEtG,SAAS,CAAC6L,IAAI,CAACM,UAAU;EACjC;AACF;AACA;AACA;EACEzJ,IAAI,EAAE1C,SAAS,CAAC8L,IAAI;EACpB;AACF;AACA;EACEvF,UAAU,EAAEvG,SAAS,CAAC,sCAAsCoE,KAAK,CAAC;IAChE8H,SAAS,EAAEjM,uBAAuB;IAClCmD,KAAK,EAAEpD,SAAS,CAACoM;EACnB,CAAC,CAAC;EACF;AACF;AACA;EACE5F,cAAc,EAAExG,SAAS,CAACoM,MAAM;EAChC;AACF;AACA;AACA;AACA;EACE3F,cAAc,EAAEzG,SAAS,CAACiM,MAAM;EAChC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEvF,kBAAkB,EAAE1G,SAAS,CAAC4L,SAAS,CAAC,CAAC5L,SAAS,CAACiM,MAAM,EAAEjM,SAAS,CAACoE,KAAK,CAAC;IACzEiI,MAAM,EAAErM,SAAS,CAACiM,MAAM;IACxB3G,KAAK,EAAEtF,SAAS,CAACiM,MAAM;IACvBvG,IAAI,EAAE1F,SAAS,CAACiM;EAClB,CAAC,CAAC,CAAC,CAAC;EACJ;AACF;AACA;EACEtF,OAAO,EAAE3G,SAAS,CAAC+L,KAAK,CAAC,CAAC,WAAW,EAAE,YAAY,EAAE,WAAW,CAAC;AACnE,CAAC,GAAG,KAAK,CAAC;AACV,eAAejH,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}