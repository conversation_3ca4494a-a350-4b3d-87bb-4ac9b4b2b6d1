{"ast": null, "code": "export { default } from './mergeSlotProps';", "map": {"version": 3, "names": ["default"], "sources": ["F:/my_work/game_logic_preview/frontend/node_modules/@mui/utils/esm/mergeSlotProps/index.js"], "sourcesContent": ["export { default } from './mergeSlotProps';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}