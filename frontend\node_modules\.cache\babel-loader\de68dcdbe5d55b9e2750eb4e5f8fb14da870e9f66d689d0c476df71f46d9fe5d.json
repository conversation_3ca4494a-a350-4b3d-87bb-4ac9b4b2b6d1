{"ast": null, "code": "'use client';\n\n/* eslint-disable jsx-a11y/aria-role */\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"slots\", \"slotProps\", \"direction\", \"orientation\", \"disabled\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport useSlotProps from '@mui/utils/useSlotProps';\nimport KeyboardArrowLeft from '../internal/svg-icons/KeyboardArrowLeft';\nimport KeyboardArrowRight from '../internal/svg-icons/KeyboardArrowRight';\nimport ButtonBase from '../ButtonBase';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport styled from '../styles/styled';\nimport tabScrollButtonClasses, { getTabScrollButtonUtilityClass } from './tabScrollButtonClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    orientation,\n    disabled\n  } = ownerState;\n  const slots = {\n    root: ['root', orientation, disabled && 'disabled']\n  };\n  return composeClasses(slots, getTabScrollButtonUtilityClass, classes);\n};\nconst TabScrollButtonRoot = styled(ButtonBase, {\n  name: 'MuiTabScrollButton',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.orientation && styles[ownerState.orientation]];\n  }\n})(({\n  ownerState\n}) => _extends({\n  width: 40,\n  flexShrink: 0,\n  opacity: 0.8,\n  [`&.${tabScrollButtonClasses.disabled}`]: {\n    opacity: 0\n  }\n}, ownerState.orientation === 'vertical' && {\n  width: '100%',\n  height: 40,\n  '& svg': {\n    transform: `rotate(${ownerState.isRtl ? -90 : 90}deg)`\n  }\n}));\nconst TabScrollButton = /*#__PURE__*/React.forwardRef(function TabScrollButton(inProps, ref) {\n  var _slots$StartScrollBut, _slots$EndScrollButto;\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiTabScrollButton'\n  });\n  const {\n      className,\n      slots = {},\n      slotProps = {},\n      direction\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const isRtl = useRtl();\n  const ownerState = _extends({\n    isRtl\n  }, props);\n  const classes = useUtilityClasses(ownerState);\n  const StartButtonIcon = (_slots$StartScrollBut = slots.StartScrollButtonIcon) != null ? _slots$StartScrollBut : KeyboardArrowLeft;\n  const EndButtonIcon = (_slots$EndScrollButto = slots.EndScrollButtonIcon) != null ? _slots$EndScrollButto : KeyboardArrowRight;\n  const startButtonIconProps = useSlotProps({\n    elementType: StartButtonIcon,\n    externalSlotProps: slotProps.startScrollButtonIcon,\n    additionalProps: {\n      fontSize: 'small'\n    },\n    ownerState\n  });\n  const endButtonIconProps = useSlotProps({\n    elementType: EndButtonIcon,\n    externalSlotProps: slotProps.endScrollButtonIcon,\n    additionalProps: {\n      fontSize: 'small'\n    },\n    ownerState\n  });\n  return /*#__PURE__*/_jsx(TabScrollButtonRoot, _extends({\n    component: \"div\",\n    className: clsx(classes.root, className),\n    ref: ref,\n    role: null,\n    ownerState: ownerState,\n    tabIndex: null\n  }, other, {\n    children: direction === 'left' ? /*#__PURE__*/_jsx(StartButtonIcon, _extends({}, startButtonIconProps)) : /*#__PURE__*/_jsx(EndButtonIcon, _extends({}, endButtonIconProps))\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? TabScrollButton.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The direction the button should indicate.\n   */\n  direction: PropTypes.oneOf(['left', 'right']).isRequired,\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * The component orientation (layout flow direction).\n   */\n  orientation: PropTypes.oneOf(['horizontal', 'vertical']).isRequired,\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    endScrollButtonIcon: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    startScrollButtonIcon: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    EndScrollButtonIcon: PropTypes.elementType,\n    StartScrollButtonIcon: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default TabScrollButton;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "composeClasses", "useRtl", "useSlotProps", "KeyboardArrowLeft", "KeyboardArrowRight", "ButtonBase", "useDefaultProps", "styled", "tabScrollButtonClasses", "getTabScrollButtonUtilityClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "orientation", "disabled", "slots", "root", "TabScrollButtonRoot", "name", "slot", "overridesResolver", "props", "styles", "width", "flexShrink", "opacity", "height", "transform", "isRtl", "TabScrollButton", "forwardRef", "inProps", "ref", "_slots$StartScrollBut", "_slots$EndScrollButto", "className", "slotProps", "direction", "other", "StartButtonIcon", "StartScrollButtonIcon", "EndButtonIcon", "EndScrollButtonIcon", "startButtonIconProps", "elementType", "externalSlotProps", "startScrollButtonIcon", "additionalProps", "fontSize", "endButtonIconProps", "endScrollButtonIcon", "component", "role", "tabIndex", "children", "process", "env", "NODE_ENV", "propTypes", "node", "object", "string", "oneOf", "isRequired", "bool", "shape", "oneOfType", "func", "sx", "arrayOf"], "sources": ["F:/my_work/game_logic_preview/frontend/node_modules/@mui/material/TabScrollButton/TabScrollButton.js"], "sourcesContent": ["'use client';\n\n/* eslint-disable jsx-a11y/aria-role */\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"slots\", \"slotProps\", \"direction\", \"orientation\", \"disabled\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport useSlotProps from '@mui/utils/useSlotProps';\nimport KeyboardArrowLeft from '../internal/svg-icons/KeyboardArrowLeft';\nimport KeyboardArrowRight from '../internal/svg-icons/KeyboardArrowRight';\nimport ButtonBase from '../ButtonBase';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport styled from '../styles/styled';\nimport tabScrollButtonClasses, { getTabScrollButtonUtilityClass } from './tabScrollButtonClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    orientation,\n    disabled\n  } = ownerState;\n  const slots = {\n    root: ['root', orientation, disabled && 'disabled']\n  };\n  return composeClasses(slots, getTabScrollButtonUtilityClass, classes);\n};\nconst TabScrollButtonRoot = styled(ButtonBase, {\n  name: 'MuiTabScrollButton',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.orientation && styles[ownerState.orientation]];\n  }\n})(({\n  ownerState\n}) => _extends({\n  width: 40,\n  flexShrink: 0,\n  opacity: 0.8,\n  [`&.${tabScrollButtonClasses.disabled}`]: {\n    opacity: 0\n  }\n}, ownerState.orientation === 'vertical' && {\n  width: '100%',\n  height: 40,\n  '& svg': {\n    transform: `rotate(${ownerState.isRtl ? -90 : 90}deg)`\n  }\n}));\nconst TabScrollButton = /*#__PURE__*/React.forwardRef(function TabScrollButton(inProps, ref) {\n  var _slots$StartScrollBut, _slots$EndScrollButto;\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiTabScrollButton'\n  });\n  const {\n      className,\n      slots = {},\n      slotProps = {},\n      direction\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const isRtl = useRtl();\n  const ownerState = _extends({\n    isRtl\n  }, props);\n  const classes = useUtilityClasses(ownerState);\n  const StartButtonIcon = (_slots$StartScrollBut = slots.StartScrollButtonIcon) != null ? _slots$StartScrollBut : KeyboardArrowLeft;\n  const EndButtonIcon = (_slots$EndScrollButto = slots.EndScrollButtonIcon) != null ? _slots$EndScrollButto : KeyboardArrowRight;\n  const startButtonIconProps = useSlotProps({\n    elementType: StartButtonIcon,\n    externalSlotProps: slotProps.startScrollButtonIcon,\n    additionalProps: {\n      fontSize: 'small'\n    },\n    ownerState\n  });\n  const endButtonIconProps = useSlotProps({\n    elementType: EndButtonIcon,\n    externalSlotProps: slotProps.endScrollButtonIcon,\n    additionalProps: {\n      fontSize: 'small'\n    },\n    ownerState\n  });\n  return /*#__PURE__*/_jsx(TabScrollButtonRoot, _extends({\n    component: \"div\",\n    className: clsx(classes.root, className),\n    ref: ref,\n    role: null,\n    ownerState: ownerState,\n    tabIndex: null\n  }, other, {\n    children: direction === 'left' ? /*#__PURE__*/_jsx(StartButtonIcon, _extends({}, startButtonIconProps)) : /*#__PURE__*/_jsx(EndButtonIcon, _extends({}, endButtonIconProps))\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? TabScrollButton.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The direction the button should indicate.\n   */\n  direction: PropTypes.oneOf(['left', 'right']).isRequired,\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * The component orientation (layout flow direction).\n   */\n  orientation: PropTypes.oneOf(['horizontal', 'vertical']).isRequired,\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    endScrollButtonIcon: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    startScrollButtonIcon: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    EndScrollButtonIcon: PropTypes.elementType,\n    StartScrollButtonIcon: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default TabScrollButton;"], "mappings": "AAAA,YAAY;;AAEZ;AACA,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,aAAa,EAAE,UAAU,CAAC;AAC7F,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,MAAM,QAAQ,yBAAyB;AAChD,OAAOC,YAAY,MAAM,yBAAyB;AAClD,OAAOC,iBAAiB,MAAM,yCAAyC;AACvE,OAAOC,kBAAkB,MAAM,0CAA0C;AACzE,OAAOC,UAAU,MAAM,eAAe;AACtC,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,sBAAsB,IAAIC,8BAA8B,QAAQ,0BAA0B;AACjG,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC,WAAW;IACXC;EACF,CAAC,GAAGH,UAAU;EACd,MAAMI,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEH,WAAW,EAAEC,QAAQ,IAAI,UAAU;EACpD,CAAC;EACD,OAAOhB,cAAc,CAACiB,KAAK,EAAER,8BAA8B,EAAEK,OAAO,CAAC;AACvE,CAAC;AACD,MAAMK,mBAAmB,GAAGZ,MAAM,CAACF,UAAU,EAAE;EAC7Ce,IAAI,EAAE,oBAAoB;EAC1BC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJX;IACF,CAAC,GAAGU,KAAK;IACT,OAAO,CAACC,MAAM,CAACN,IAAI,EAAEL,UAAU,CAACE,WAAW,IAAIS,MAAM,CAACX,UAAU,CAACE,WAAW,CAAC,CAAC;EAChF;AACF,CAAC,CAAC,CAAC,CAAC;EACFF;AACF,CAAC,KAAKlB,QAAQ,CAAC;EACb8B,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,CAAC;EACbC,OAAO,EAAE,GAAG;EACZ,CAAC,KAAKnB,sBAAsB,CAACQ,QAAQ,EAAE,GAAG;IACxCW,OAAO,EAAE;EACX;AACF,CAAC,EAAEd,UAAU,CAACE,WAAW,KAAK,UAAU,IAAI;EAC1CU,KAAK,EAAE,MAAM;EACbG,MAAM,EAAE,EAAE;EACV,OAAO,EAAE;IACPC,SAAS,EAAE,UAAUhB,UAAU,CAACiB,KAAK,GAAG,CAAC,EAAE,GAAG,EAAE;EAClD;AACF,CAAC,CAAC,CAAC;AACH,MAAMC,eAAe,GAAG,aAAalC,KAAK,CAACmC,UAAU,CAAC,SAASD,eAAeA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC3F,IAAIC,qBAAqB,EAAEC,qBAAqB;EAChD,MAAMb,KAAK,GAAGjB,eAAe,CAAC;IAC5BiB,KAAK,EAAEU,OAAO;IACdb,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFiB,SAAS;MACTpB,KAAK,GAAG,CAAC,CAAC;MACVqB,SAAS,GAAG,CAAC,CAAC;MACdC;IACF,CAAC,GAAGhB,KAAK;IACTiB,KAAK,GAAG9C,6BAA6B,CAAC6B,KAAK,EAAE3B,SAAS,CAAC;EACzD,MAAMkC,KAAK,GAAG7B,MAAM,CAAC,CAAC;EACtB,MAAMY,UAAU,GAAGlB,QAAQ,CAAC;IAC1BmC;EACF,CAAC,EAAEP,KAAK,CAAC;EACT,MAAMT,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAM4B,eAAe,GAAG,CAACN,qBAAqB,GAAGlB,KAAK,CAACyB,qBAAqB,KAAK,IAAI,GAAGP,qBAAqB,GAAGhC,iBAAiB;EACjI,MAAMwC,aAAa,GAAG,CAACP,qBAAqB,GAAGnB,KAAK,CAAC2B,mBAAmB,KAAK,IAAI,GAAGR,qBAAqB,GAAGhC,kBAAkB;EAC9H,MAAMyC,oBAAoB,GAAG3C,YAAY,CAAC;IACxC4C,WAAW,EAAEL,eAAe;IAC5BM,iBAAiB,EAAET,SAAS,CAACU,qBAAqB;IAClDC,eAAe,EAAE;MACfC,QAAQ,EAAE;IACZ,CAAC;IACDrC;EACF,CAAC,CAAC;EACF,MAAMsC,kBAAkB,GAAGjD,YAAY,CAAC;IACtC4C,WAAW,EAAEH,aAAa;IAC1BI,iBAAiB,EAAET,SAAS,CAACc,mBAAmB;IAChDH,eAAe,EAAE;MACfC,QAAQ,EAAE;IACZ,CAAC;IACDrC;EACF,CAAC,CAAC;EACF,OAAO,aAAaF,IAAI,CAACQ,mBAAmB,EAAExB,QAAQ,CAAC;IACrD0D,SAAS,EAAE,KAAK;IAChBhB,SAAS,EAAEtC,IAAI,CAACe,OAAO,CAACI,IAAI,EAAEmB,SAAS,CAAC;IACxCH,GAAG,EAAEA,GAAG;IACRoB,IAAI,EAAE,IAAI;IACVzC,UAAU,EAAEA,UAAU;IACtB0C,QAAQ,EAAE;EACZ,CAAC,EAAEf,KAAK,EAAE;IACRgB,QAAQ,EAAEjB,SAAS,KAAK,MAAM,GAAG,aAAa5B,IAAI,CAAC8B,eAAe,EAAE9C,QAAQ,CAAC,CAAC,CAAC,EAAEkD,oBAAoB,CAAC,CAAC,GAAG,aAAalC,IAAI,CAACgC,aAAa,EAAEhD,QAAQ,CAAC,CAAC,CAAC,EAAEwD,kBAAkB,CAAC;EAC7K,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFM,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG5B,eAAe,CAAC6B,SAAS,CAAC,yBAAyB;EACzF;EACA;EACA;EACA;EACA;AACF;AACA;EACEJ,QAAQ,EAAE1D,SAAS,CAAC+D,IAAI;EACxB;AACF;AACA;EACE/C,OAAO,EAAEhB,SAAS,CAACgE,MAAM;EACzB;AACF;AACA;EACEzB,SAAS,EAAEvC,SAAS,CAACiE,MAAM;EAC3B;AACF;AACA;EACExB,SAAS,EAAEzC,SAAS,CAACkE,KAAK,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAACC,UAAU;EACxD;AACF;AACA;AACA;EACEjD,QAAQ,EAAElB,SAAS,CAACoE,IAAI;EACxB;AACF;AACA;EACEnD,WAAW,EAAEjB,SAAS,CAACkE,KAAK,CAAC,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC,CAACC,UAAU;EACnE;AACF;AACA;AACA;AACA;EACE3B,SAAS,EAAExC,SAAS,CAACqE,KAAK,CAAC;IACzBf,mBAAmB,EAAEtD,SAAS,CAACsE,SAAS,CAAC,CAACtE,SAAS,CAACuE,IAAI,EAAEvE,SAAS,CAACgE,MAAM,CAAC,CAAC;IAC5Ed,qBAAqB,EAAElD,SAAS,CAACsE,SAAS,CAAC,CAACtE,SAAS,CAACuE,IAAI,EAAEvE,SAAS,CAACgE,MAAM,CAAC;EAC/E,CAAC,CAAC;EACF;AACF;AACA;AACA;EACE7C,KAAK,EAAEnB,SAAS,CAACqE,KAAK,CAAC;IACrBvB,mBAAmB,EAAE9C,SAAS,CAACgD,WAAW;IAC1CJ,qBAAqB,EAAE5C,SAAS,CAACgD;EACnC,CAAC,CAAC;EACF;AACF;AACA;EACEwB,EAAE,EAAExE,SAAS,CAACsE,SAAS,CAAC,CAACtE,SAAS,CAACyE,OAAO,CAACzE,SAAS,CAACsE,SAAS,CAAC,CAACtE,SAAS,CAACuE,IAAI,EAAEvE,SAAS,CAACgE,MAAM,EAAEhE,SAAS,CAACoE,IAAI,CAAC,CAAC,CAAC,EAAEpE,SAAS,CAACuE,IAAI,EAAEvE,SAAS,CAACgE,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAe/B,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}