{"ast": null, "code": "import isHostComponent from '@mui/utils/isHostComponent';\nconst shouldSpreadAdditionalProps = Slot => {\n  return !Slot || !isHostComponent(Slot);\n};\nexport default shouldSpreadAdditionalProps;", "map": {"version": 3, "names": ["isHostComponent", "shouldSpreadAdditionalProps", "Slot"], "sources": ["F:/my_work/game_logic_preview/frontend/node_modules/@mui/material/utils/shouldSpreadAdditionalProps.js"], "sourcesContent": ["import isHostComponent from '@mui/utils/isHostComponent';\nconst shouldSpreadAdditionalProps = Slot => {\n  return !Slot || !isHostComponent(Slot);\n};\nexport default shouldSpreadAdditionalProps;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,4BAA4B;AACxD,MAAMC,2BAA2B,GAAGC,IAAI,IAAI;EAC1C,OAAO,CAACA,IAAI,IAAI,CAACF,eAAe,CAACE,IAAI,CAAC;AACxC,CAAC;AACD,eAAeD,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}