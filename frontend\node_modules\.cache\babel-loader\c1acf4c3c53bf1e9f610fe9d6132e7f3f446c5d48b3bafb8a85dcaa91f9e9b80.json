{"ast": null, "code": "// Supports determination of isControlled().\n// Controlled input accepts its current value as a prop.\n//\n// @see https://facebook.github.io/react/docs/forms.html#controlled-components\n// @param value\n// @returns {boolean} true if string (including '') or number (including zero)\nexport function hasValue(value) {\n  return value != null && !(Array.isArray(value) && value.length === 0);\n}\n\n// Determine if field is empty or filled.\n// Response determines if label is presented above field or as placeholder.\n//\n// @param obj\n// @param SSR\n// @returns {boolean} False when not present or empty string.\n//                    True when any number or string with length.\nexport function isFilled(obj, SSR = false) {\n  return obj && (hasValue(obj.value) && obj.value !== '' || SSR && hasValue(obj.defaultValue) && obj.defaultValue !== '');\n}\n\n// Determine if an Input is adorned on start.\n// It's corresponding to the left with LTR.\n//\n// @param obj\n// @returns {boolean} False when no adornments.\n//                    True when adorned at the start.\nexport function isAdornedStart(obj) {\n  return obj.startAdornment;\n}", "map": {"version": 3, "names": ["hasValue", "value", "Array", "isArray", "length", "isFilled", "obj", "SSR", "defaultValue", "isAdornedStart", "startAdornment"], "sources": ["F:/my_work/game_logic_preview/frontend/node_modules/@mui/material/InputBase/utils.js"], "sourcesContent": ["// Supports determination of isControlled().\n// Controlled input accepts its current value as a prop.\n//\n// @see https://facebook.github.io/react/docs/forms.html#controlled-components\n// @param value\n// @returns {boolean} true if string (including '') or number (including zero)\nexport function hasValue(value) {\n  return value != null && !(Array.isArray(value) && value.length === 0);\n}\n\n// Determine if field is empty or filled.\n// Response determines if label is presented above field or as placeholder.\n//\n// @param obj\n// @param SSR\n// @returns {boolean} False when not present or empty string.\n//                    True when any number or string with length.\nexport function isFilled(obj, SSR = false) {\n  return obj && (hasValue(obj.value) && obj.value !== '' || SSR && hasValue(obj.defaultValue) && obj.defaultValue !== '');\n}\n\n// Determine if an Input is adorned on start.\n// It's corresponding to the left with LTR.\n//\n// @param obj\n// @returns {boolean} False when no adornments.\n//                    True when adorned at the start.\nexport function isAdornedStart(obj) {\n  return obj.startAdornment;\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAOA,KAAK,IAAI,IAAI,IAAI,EAAEC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,IAAIA,KAAK,CAACG,MAAM,KAAK,CAAC,CAAC;AACvE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,QAAQA,CAACC,GAAG,EAAEC,GAAG,GAAG,KAAK,EAAE;EACzC,OAAOD,GAAG,KAAKN,QAAQ,CAACM,GAAG,CAACL,KAAK,CAAC,IAAIK,GAAG,CAACL,KAAK,KAAK,EAAE,IAAIM,GAAG,IAAIP,QAAQ,CAACM,GAAG,CAACE,YAAY,CAAC,IAAIF,GAAG,CAACE,YAAY,KAAK,EAAE,CAAC;AACzH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACH,GAAG,EAAE;EAClC,OAAOA,GAAG,CAACI,cAAc;AAC3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}