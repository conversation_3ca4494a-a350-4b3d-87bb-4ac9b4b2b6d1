{"ast": null, "code": "import * as React from 'react';\n/**\n * Provides information about the current step in Stepper.\n */\nconst StepperContext = /*#__PURE__*/React.createContext({});\nif (process.env.NODE_ENV !== 'production') {\n  StepperContext.displayName = 'StepperContext';\n}\n\n/**\n * Returns the current StepperContext or an empty object if no StepperContext\n * has been defined in the component tree.\n */\nexport function useStepperContext() {\n  return React.useContext(StepperContext);\n}\nexport default StepperContext;", "map": {"version": 3, "names": ["React", "StepperContext", "createContext", "process", "env", "NODE_ENV", "displayName", "useStepperContext", "useContext"], "sources": ["F:/my_work/game_logic_preview/frontend/node_modules/@mui/material/Stepper/StepperContext.js"], "sourcesContent": ["import * as React from 'react';\n/**\n * Provides information about the current step in Stepper.\n */\nconst StepperContext = /*#__PURE__*/React.createContext({});\nif (process.env.NODE_ENV !== 'production') {\n  StepperContext.displayName = 'StepperContext';\n}\n\n/**\n * Returns the current StepperContext or an empty object if no StepperContext\n * has been defined in the component tree.\n */\nexport function useStepperContext() {\n  return React.useContext(StepperContext);\n}\nexport default StepperContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B;AACA;AACA;AACA,MAAMC,cAAc,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,CAAC,CAAC,CAAC;AAC3D,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,cAAc,CAACK,WAAW,GAAG,gBAAgB;AAC/C;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASC,iBAAiBA,CAAA,EAAG;EAClC,OAAOP,KAAK,CAACQ,UAAU,CAACP,cAAc,CAAC;AACzC;AACA,eAAeA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}