{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getTableRowUtilityClass(slot) {\n  return generateUtilityClass('MuiTableRow', slot);\n}\nconst tableRowClasses = generateUtilityClasses('MuiTableRow', ['root', 'selected', 'hover', 'head', 'footer']);\nexport default tableRowClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getTableRowUtilityClass", "slot", "tableRowClasses"], "sources": ["F:/my_work/game_logic_preview/frontend/node_modules/@mui/material/TableRow/tableRowClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getTableRowUtilityClass(slot) {\n  return generateUtilityClass('MuiTableRow', slot);\n}\nconst tableRowClasses = generateUtilityClasses('MuiTableRow', ['root', 'selected', 'hover', 'head', 'footer']);\nexport default tableRowClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,uBAAuBA,CAACC,IAAI,EAAE;EAC5C,OAAOF,oBAAoB,CAAC,aAAa,EAAEE,IAAI,CAAC;AAClD;AACA,MAAMC,eAAe,GAAGJ,sBAAsB,CAAC,aAAa,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;AAC9G,eAAeI,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}