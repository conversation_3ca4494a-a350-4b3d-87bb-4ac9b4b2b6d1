[{"F:\\my_work\\game_logic_preview\\frontend\\src\\index.tsx": "1", "F:\\my_work\\game_logic_preview\\frontend\\src\\App.tsx": "2", "F:\\my_work\\game_logic_preview\\frontend\\src\\pages\\SimulationEditor.tsx": "3", "F:\\my_work\\game_logic_preview\\frontend\\src\\pages\\EntityManager.tsx": "4", "F:\\my_work\\game_logic_preview\\frontend\\src\\pages\\SimulationResults.tsx": "5", "F:\\my_work\\game_logic_preview\\frontend\\src\\services\\api.ts": "6", "F:\\my_work\\game_logic_preview\\frontend\\src\\types\\index.ts": "7"}, {"size": 786, "mtime": 1754891865700, "results": "8", "hashOfConfig": "9"}, {"size": 2604, "mtime": 1754891879447, "results": "10", "hashOfConfig": "9"}, {"size": 10112, "mtime": 1754891984336, "results": "11", "hashOfConfig": "9"}, {"size": 8507, "mtime": 1754891946449, "results": "12", "hashOfConfig": "9"}, {"size": 11596, "mtime": 1754892024260, "results": "13", "hashOfConfig": "9"}, {"size": 3755, "mtime": 1754891914278, "results": "14", "hashOfConfig": "9"}, {"size": 1474, "mtime": 1754891892449, "results": "15", "hashOfConfig": "9"}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "oegm3b", {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "F:\\my_work\\game_logic_preview\\frontend\\src\\index.tsx", [], [], "F:\\my_work\\game_logic_preview\\frontend\\src\\App.tsx", [], [], "F:\\my_work\\game_logic_preview\\frontend\\src\\pages\\SimulationEditor.tsx", [], [], "F:\\my_work\\game_logic_preview\\frontend\\src\\pages\\EntityManager.tsx", ["37"], [], "F:\\my_work\\game_logic_preview\\frontend\\src\\pages\\SimulationResults.tsx", ["38"], [], "F:\\my_work\\game_logic_preview\\frontend\\src\\services\\api.ts", [], [], "F:\\my_work\\game_logic_preview\\frontend\\src\\types\\index.ts", [], [], {"ruleId": "39", "severity": 1, "message": "40", "line": 10, "column": 3, "nodeType": "41", "messageId": "42", "endLine": 10, "endColumn": 6}, {"ruleId": "39", "severity": 1, "message": "43", "line": 15, "column": 3, "nodeType": "41", "messageId": "42", "endLine": 15, "endColumn": 10}, "@typescript-eslint/no-unused-vars", "'Fab' is defined but never used.", "Identifier", "unusedVar", "'Divider' is defined but never used."]