{"ast": null, "code": "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar printWarning = function () {};\nif (process.env.NODE_ENV !== 'production') {\n  var ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\n  var loggedTypeFailures = {};\n  var has = require('./lib/has');\n  printWarning = function (text) {\n    var message = 'Warning: ' + text;\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) {/**/}\n  };\n}\n\n/**\n * Assert that the values match with the type specs.\n * Error messages are memorized and will only be shown once.\n *\n * @param {object} typeSpecs Map of name to a ReactPropType\n * @param {object} values Runtime values that need to be type-checked\n * @param {string} location e.g. \"prop\", \"context\", \"child context\"\n * @param {string} componentName Name of the component for error messages.\n * @param {?Function} getStack Returns the component stack.\n * @private\n */\nfunction checkPropTypes(typeSpecs, values, location, componentName, getStack) {\n  if (process.env.NODE_ENV !== 'production') {\n    for (var typeSpecName in typeSpecs) {\n      if (has(typeSpecs, typeSpecName)) {\n        var error;\n        // Prop type validation may throw. In case they do, we don't want to\n        // fail the render phase where it didn't fail before. So we log it.\n        // After these have been cleaned up, we'll let them throw.\n        try {\n          // This is intentionally an invariant that gets caught. It's the same\n          // behavior as without this statement except with a better message.\n          if (typeof typeSpecs[typeSpecName] !== 'function') {\n            var err = Error((componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' + 'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' + 'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.');\n            err.name = 'Invariant Violation';\n            throw err;\n          }\n          error = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, ReactPropTypesSecret);\n        } catch (ex) {\n          error = ex;\n        }\n        if (error && !(error instanceof Error)) {\n          printWarning((componentName || 'React class') + ': type specification of ' + location + ' `' + typeSpecName + '` is invalid; the type checker ' + 'function must return `null` or an `Error` but returned a ' + typeof error + '. ' + 'You may have forgotten to pass an argument to the type checker ' + 'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' + 'shape all require an argument).');\n        }\n        if (error instanceof Error && !(error.message in loggedTypeFailures)) {\n          // Only monitor this failure once because there tends to be a lot of the\n          // same error.\n          loggedTypeFailures[error.message] = true;\n          var stack = getStack ? getStack() : '';\n          printWarning('Failed ' + location + ' type: ' + error.message + (stack != null ? stack : ''));\n        }\n      }\n    }\n  }\n}\n\n/**\n * Resets warning cache when testing.\n *\n * @private\n */\ncheckPropTypes.resetWarningCache = function () {\n  if (process.env.NODE_ENV !== 'production') {\n    loggedTypeFailures = {};\n  }\n};\nmodule.exports = checkPropTypes;", "map": {"version": 3, "names": ["printWarning", "process", "env", "NODE_ENV", "ReactPropTypesSecret", "require", "loggedTypeFailures", "has", "text", "message", "console", "error", "Error", "x", "checkPropTypes", "typeSpecs", "values", "location", "componentName", "getStack", "typeSpecName", "err", "name", "ex", "stack", "resetWarningCache", "module", "exports"], "sources": ["F:/my_work/game_logic_preview/frontend/node_modules/prop-types/checkPropTypes.js"], "sourcesContent": ["/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar printWarning = function() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  var ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\n  var loggedTypeFailures = {};\n  var has = require('./lib/has');\n\n  printWarning = function(text) {\n    var message = 'Warning: ' + text;\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) { /**/ }\n  };\n}\n\n/**\n * Assert that the values match with the type specs.\n * Error messages are memorized and will only be shown once.\n *\n * @param {object} typeSpecs Map of name to a ReactPropType\n * @param {object} values Runtime values that need to be type-checked\n * @param {string} location e.g. \"prop\", \"context\", \"child context\"\n * @param {string} componentName Name of the component for error messages.\n * @param {?Function} getStack Returns the component stack.\n * @private\n */\nfunction checkPropTypes(typeSpecs, values, location, componentName, getStack) {\n  if (process.env.NODE_ENV !== 'production') {\n    for (var typeSpecName in typeSpecs) {\n      if (has(typeSpecs, typeSpecName)) {\n        var error;\n        // Prop type validation may throw. In case they do, we don't want to\n        // fail the render phase where it didn't fail before. So we log it.\n        // After these have been cleaned up, we'll let them throw.\n        try {\n          // This is intentionally an invariant that gets caught. It's the same\n          // behavior as without this statement except with a better message.\n          if (typeof typeSpecs[typeSpecName] !== 'function') {\n            var err = Error(\n              (componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' +\n              'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' +\n              'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.'\n            );\n            err.name = 'Invariant Violation';\n            throw err;\n          }\n          error = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, ReactPropTypesSecret);\n        } catch (ex) {\n          error = ex;\n        }\n        if (error && !(error instanceof Error)) {\n          printWarning(\n            (componentName || 'React class') + ': type specification of ' +\n            location + ' `' + typeSpecName + '` is invalid; the type checker ' +\n            'function must return `null` or an `Error` but returned a ' + typeof error + '. ' +\n            'You may have forgotten to pass an argument to the type checker ' +\n            'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' +\n            'shape all require an argument).'\n          );\n        }\n        if (error instanceof Error && !(error.message in loggedTypeFailures)) {\n          // Only monitor this failure once because there tends to be a lot of the\n          // same error.\n          loggedTypeFailures[error.message] = true;\n\n          var stack = getStack ? getStack() : '';\n\n          printWarning(\n            'Failed ' + location + ' type: ' + error.message + (stack != null ? stack : '')\n          );\n        }\n      }\n    }\n  }\n}\n\n/**\n * Resets warning cache when testing.\n *\n * @private\n */\ncheckPropTypes.resetWarningCache = function() {\n  if (process.env.NODE_ENV !== 'production') {\n    loggedTypeFailures = {};\n  }\n}\n\nmodule.exports = checkPropTypes;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,IAAIA,YAAY,GAAG,SAAAA,CAAA,EAAW,CAAC,CAAC;AAEhC,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC,IAAIC,oBAAoB,GAAGC,OAAO,CAAC,4BAA4B,CAAC;EAChE,IAAIC,kBAAkB,GAAG,CAAC,CAAC;EAC3B,IAAIC,GAAG,GAAGF,OAAO,CAAC,WAAW,CAAC;EAE9BL,YAAY,GAAG,SAAAA,CAASQ,IAAI,EAAE;IAC5B,IAAIC,OAAO,GAAG,WAAW,GAAGD,IAAI;IAChC,IAAI,OAAOE,OAAO,KAAK,WAAW,EAAE;MAClCA,OAAO,CAACC,KAAK,CAACF,OAAO,CAAC;IACxB;IACA,IAAI;MACF;MACA;MACA;MACA,MAAM,IAAIG,KAAK,CAACH,OAAO,CAAC;IAC1B,CAAC,CAAC,OAAOI,CAAC,EAAE,CAAE;EAChB,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,QAAQ,EAAE;EAC5E,IAAIlB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,KAAK,IAAIiB,YAAY,IAAIL,SAAS,EAAE;MAClC,IAAIR,GAAG,CAACQ,SAAS,EAAEK,YAAY,CAAC,EAAE;QAChC,IAAIT,KAAK;QACT;QACA;QACA;QACA,IAAI;UACF;UACA;UACA,IAAI,OAAOI,SAAS,CAACK,YAAY,CAAC,KAAK,UAAU,EAAE;YACjD,IAAIC,GAAG,GAAGT,KAAK,CACb,CAACM,aAAa,IAAI,aAAa,IAAI,IAAI,GAAGD,QAAQ,GAAG,SAAS,GAAGG,YAAY,GAAG,gBAAgB,GAChG,8EAA8E,GAAG,OAAOL,SAAS,CAACK,YAAY,CAAC,GAAG,IAAI,GACtH,+FACF,CAAC;YACDC,GAAG,CAACC,IAAI,GAAG,qBAAqB;YAChC,MAAMD,GAAG;UACX;UACAV,KAAK,GAAGI,SAAS,CAACK,YAAY,CAAC,CAACJ,MAAM,EAAEI,YAAY,EAAEF,aAAa,EAAED,QAAQ,EAAE,IAAI,EAAEb,oBAAoB,CAAC;QAC5G,CAAC,CAAC,OAAOmB,EAAE,EAAE;UACXZ,KAAK,GAAGY,EAAE;QACZ;QACA,IAAIZ,KAAK,IAAI,EAAEA,KAAK,YAAYC,KAAK,CAAC,EAAE;UACtCZ,YAAY,CACV,CAACkB,aAAa,IAAI,aAAa,IAAI,0BAA0B,GAC7DD,QAAQ,GAAG,IAAI,GAAGG,YAAY,GAAG,iCAAiC,GAClE,2DAA2D,GAAG,OAAOT,KAAK,GAAG,IAAI,GACjF,iEAAiE,GACjE,gEAAgE,GAChE,iCACF,CAAC;QACH;QACA,IAAIA,KAAK,YAAYC,KAAK,IAAI,EAAED,KAAK,CAACF,OAAO,IAAIH,kBAAkB,CAAC,EAAE;UACpE;UACA;UACAA,kBAAkB,CAACK,KAAK,CAACF,OAAO,CAAC,GAAG,IAAI;UAExC,IAAIe,KAAK,GAAGL,QAAQ,GAAGA,QAAQ,CAAC,CAAC,GAAG,EAAE;UAEtCnB,YAAY,CACV,SAAS,GAAGiB,QAAQ,GAAG,SAAS,GAAGN,KAAK,CAACF,OAAO,IAAIe,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAG,EAAE,CAChF,CAAC;QACH;MACF;IACF;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACAV,cAAc,CAACW,iBAAiB,GAAG,YAAW;EAC5C,IAAIxB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCG,kBAAkB,GAAG,CAAC,CAAC;EACzB;AACF,CAAC;AAEDoB,MAAM,CAACC,OAAO,GAAGb,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}