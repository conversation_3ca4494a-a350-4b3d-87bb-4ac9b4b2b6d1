{"ast": null, "code": "export default function memoize(fn) {\n  const cache = {};\n  return arg => {\n    if (cache[arg] === undefined) {\n      cache[arg] = fn(arg);\n    }\n    return cache[arg];\n  };\n}", "map": {"version": 3, "names": ["memoize", "fn", "cache", "arg", "undefined"], "sources": ["F:/my_work/game_logic_preview/frontend/node_modules/@mui/system/esm/memoize.js"], "sourcesContent": ["export default function memoize(fn) {\n  const cache = {};\n  return arg => {\n    if (cache[arg] === undefined) {\n      cache[arg] = fn(arg);\n    }\n    return cache[arg];\n  };\n}"], "mappings": "AAAA,eAAe,SAASA,OAAOA,CAACC,EAAE,EAAE;EAClC,MAAMC,KAAK,GAAG,CAAC,CAAC;EAChB,OAAOC,GAAG,IAAI;IACZ,IAAID,KAAK,CAACC,GAAG,CAAC,KAAKC,SAAS,EAAE;MAC5BF,KAAK,CAACC,GAAG,CAAC,GAAGF,EAAE,CAACE,GAAG,CAAC;IACtB;IACA,OAAOD,KAAK,CAACC,GAAG,CAAC;EACnB,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}